import { Component, OnInit } from '@angular/core';
import { Platform, AlertController, LoadingController } from '@ionic/angular';
import { FCMService } from '../../services/fcm.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-fcm-debug',
  template: `
    <ion-card>
      <ion-card-header>
        <ion-card-title>🚨 FCM Debug Panel</ion-card-title>
      </ion-card-header>
      
      <ion-card-content>
        <div class="debug-section">
          <h3>📱 Device Info</h3>
          <p><strong>Platform:</strong> {{ platformInfo }}</p>
          <p><strong>Is Capacitor:</strong> {{ isCapacitor }}</p>
          <p><strong>Google Play Services:</strong> {{ googlePlayStatus }}</p>
        </div>

        <div class="debug-section">
          <h3>🎫 FCM Token</h3>
          <p><strong>Status:</strong> {{ tokenStatus }}</p>
          <p><strong>Token (first 20 chars):</strong> {{ tokenPreview }}</p>
          <ion-button expand="block" fill="outline" (click)="refreshToken()">
            🔄 Refresh FCM Token
          </ion-button>
        </div>

        <div class="debug-section">
          <h3>🌐 Backend Connection</h3>
          <p><strong>API URL:</strong> {{ apiUrl }}</p>
          <p><strong>Connection Status:</strong> {{ connectionStatus }}</p>
          <ion-button expand="block" fill="outline" (click)="testBackendConnection()">
            🔗 Test Backend Connection
          </ion-button>
        </div>

        <div class="debug-section">
          <h3>📬 Notification Test</h3>
          <ion-button expand="block" fill="outline" (click)="testLocalNotification()">
            🔔 Test Local Notification
          </ion-button>
          <ion-button expand="block" (click)="sendTestNotification()" [disabled]="!fcmToken">
            📤 Send Test FCM Notification
          </ion-button>
        </div>

        <div class="debug-section">
          <h3>📋 Debug Logs</h3>
          <div class="log-container">
            <div *ngFor="let log of debugLogs" [class]="'log-' + log.type">
              <small>{{ log.timestamp }}</small> - {{ log.message }}
            </div>
          </div>
          <ion-button expand="block" fill="clear" (click)="clearLogs()">
            🗑️ Clear Logs
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  `,
  styles: [`
    .debug-section {
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    
    .log-container {
      max-height: 200px;
      overflow-y: auto;
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
    
    .log-info { color: #007bff; }
    .log-success { color: #28a745; }
    .log-warning { color: #ffc107; }
    .log-error { color: #dc3545; }
  `]
})
export class FCMDebugComponent implements OnInit {
  platformInfo = '';
  isCapacitor = false;
  googlePlayStatus = 'Checking...';
  tokenStatus = 'Not checked';
  tokenPreview = 'None';
  fcmToken = '';
  apiUrl = environment.apiUrl;
  connectionStatus = 'Not tested';
  debugLogs: Array<{type: string, message: string, timestamp: string}> = [];

  constructor(
    private platform: Platform,
    private fcmService: FCMService,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.initializeDebugInfo();
  }

  private initializeDebugInfo() {
    // Platform info
    this.platformInfo = this.platform.platforms().join(', ');
    this.isCapacitor = this.platform.is('capacitor');
    
    this.addLog('info', 'FCM Debug panel initialized');
    this.addLog('info', `Platform: ${this.platformInfo}`);
    this.addLog('info', `Is Capacitor: ${this.isCapacitor}`);
    
    // Check FCM token
    this.checkFCMToken();
  }

  private addLog(type: string, message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.debugLogs.unshift({ type, message, timestamp });
    
    // Keep only last 50 logs
    if (this.debugLogs.length > 50) {
      this.debugLogs = this.debugLogs.slice(0, 50);
    }
  }

  async checkFCMToken() {
    try {
      this.addLog('info', 'Checking FCM token...');
      
      if (!this.isCapacitor) {
        this.tokenStatus = 'Browser mode - FCM not available';
        this.addLog('warning', 'Running in browser - FCM not available');
        return;
      }

      const token = await this.fcmService.getToken();
      
      if (token) {
        this.fcmToken = token;
        this.tokenPreview = token.substring(0, 20) + '...';
        this.tokenStatus = 'Token retrieved successfully';
        this.addLog('success', `FCM token retrieved: ${this.tokenPreview}`);
      } else {
        this.tokenStatus = 'Failed to get token';
        this.addLog('error', 'Failed to retrieve FCM token');
      }
    } catch (error) {
      this.tokenStatus = 'Error getting token';
      this.addLog('error', `FCM token error: ${error}`);
    }
  }

  async refreshToken() {
    const loading = await this.loadingController.create({
      message: 'Refreshing FCM token...'
    });
    await loading.present();

    try {
      this.addLog('info', 'Refreshing FCM token...');
      await this.fcmService.initPush();
      await this.checkFCMToken();
      this.addLog('success', 'FCM token refreshed');
    } catch (error) {
      this.addLog('error', `Token refresh failed: ${error}`);
    }

    await loading.dismiss();
  }

  async testBackendConnection() {
    const loading = await this.loadingController.create({
      message: 'Testing backend connection...'
    });
    await loading.present();

    try {
      this.addLog('info', `Testing connection to: ${this.apiUrl}`);
      
      const response = await this.http.get(`${this.apiUrl}/test-connection`, {
        timeout: 5000
      }).toPromise();
      
      this.connectionStatus = 'Connected successfully';
      this.addLog('success', 'Backend connection successful');
    } catch (error) {
      this.connectionStatus = `Connection failed: ${error}`;
      this.addLog('error', `Backend connection failed: ${error}`);
    }

    await loading.dismiss();
  }

  async testLocalNotification() {
    try {
      this.addLog('info', 'Testing local notification...');
      
      // Simple alert as local notification test
      const alert = await this.alertController.create({
        header: '🔔 Test Notification',
        message: 'This is a test local notification. If you see this, local notifications work!',
        buttons: ['OK']
      });
      
      await alert.present();
      this.addLog('success', 'Local notification test completed');
    } catch (error) {
      this.addLog('error', `Local notification test failed: ${error}`);
    }
  }

  async sendTestNotification() {
    if (!this.fcmToken) {
      this.addLog('error', 'No FCM token available for test');
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Sending test notification...'
    });
    await loading.present();

    try {
      this.addLog('info', 'Sending test FCM notification...');
      
      const payload = {
        fcm_token: this.fcmToken,
        title: '🚨 Test Emergency Alert',
        body: 'This is a test notification from FCM Debug Panel',
        category: 'earthquake'
      };

      const response = await this.http.post(`${this.apiUrl}/send-test-notification`, payload).toPromise();
      
      this.addLog('success', 'Test notification sent successfully');
    } catch (error) {
      this.addLog('error', `Test notification failed: ${error}`);
    }

    await loading.dismiss();
  }

  clearLogs() {
    this.debugLogs = [];
    this.addLog('info', 'Debug logs cleared');
  }
}
