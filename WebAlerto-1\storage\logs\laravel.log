[2025-05-28 22:56:40] local.ERROR: Database file at path [C:\Users\<USER>\CAPSTONE\WebAlerto-1\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():164}(1)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select exists (...', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():164}(1)
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#37 {main}
"} 
[2025-05-28 22:57:13] local.ERROR: Database file at path [C:\Users\<USER>\CAPSTONE\WebAlerto-1\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():164}(1)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select exists (...', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():164}(1)
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#37 {main}
"} 
[2025-05-28 22:58:17] local.ERROR: Database file at path [C:\Users\<USER>\CAPSTONE\WebAlerto-1\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from \"cache\") at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(589): Illuminate\\Database\\Connection->getPdo()
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::affectingStatement():581}('delete from \"ca...', Array)
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-05-28 23:53:47] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#48 {main}
"} 
[2025-05-28 23:54:01] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#21 {main}
"} 
[2025-05-28 23:54:28] local.INFO: API request received for evacuation centers  
[2025-05-28 23:54:28] local.INFO: Retrieved evacuation centers from database {"count":0} 
[2025-05-28 23:54:28] local.INFO: Returning formatted evacuation centers {"count":0} 
[2025-05-28 23:54:46] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#48 {main}
"} 
[2025-05-28 23:54:47] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#21 {main}
"} 
[2025-05-28 23:55:07] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#48 {main}
"} 
[2025-05-28 23:55:08] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():81}('')
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\CAP...')
#21 {main}
"} 
[2025-05-28 23:58:08] local.ERROR: Failed to send evacuation center notification: Call to undefined method App\Models\PushNotification::sendToAllUsers()  
[2025-05-28 23:58:19] local.INFO: API request received for evacuation centers  
[2025-05-28 23:58:19] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-28 23:58:19] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-28 23:58:28] local.INFO: API request received for evacuation centers  
[2025-05-28 23:58:28] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-28 23:58:28] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 00:02:30] local.INFO: API request received for evacuation centers  
[2025-05-29 00:02:30] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 00:02:30] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 00:05:46] local.INFO: API request received for evacuation centers  
[2025-05-29 00:05:46] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 00:05:46] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 00:07:16] local.INFO: API request received for evacuation centers  
[2025-05-29 00:07:16] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 00:07:16] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 00:07:54] local.INFO: API request received for evacuation centers  
[2025-05-29 00:07:54] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 00:07:54] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 00:53:09] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:09] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:14] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:14] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:16] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:16] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:25] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:25] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:26] local.INFO: No active device tokens found  
[2025-05-29 00:53:27] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:27] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:37] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:37] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:42] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:42] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 00:53:43] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 00:53:43] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:00:24] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:00:24] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:01:38] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:01:38] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:02:11] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:02:11] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:02:16] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:02:16] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:02:19] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:02:19] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:02:22] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:02:22] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:02:22] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:02:22] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:25:44] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:25:44] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:25:56] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:25:56] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:26:13] local.ERROR: PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Dev...', false)
#2 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Device To...', true)
#4 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Device To...', true)
#5 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Device To...')
#6 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#8 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-05-29 01:35:00] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:35:00] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:35:05] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:35:05] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:36:59] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:36:59] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:37:02] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:37:02] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:37:05] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:37:05] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:37:09] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:37:09] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:37:10] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:37:10] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:42:06] local.INFO: Device token registered successfully {"token_hash":"00737f9d891ef5bea24aabbd9516a4e196c8b5efa62da7088c74bdcb2f474b9e","device_type":"android","project_id":"last-5acaf"} 
[2025-05-29 01:43:27] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:43:27] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:43:32] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:43:32] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:43:33] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:43:33] local.ERROR: Firebase service account file not found. Please check that the file exists at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:43:34] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:43:34] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:45:53] local.INFO: API request received for evacuation centers  
[2025-05-29 01:45:54] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 01:45:54] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 01:46:00] local.INFO: API request received for evacuation centers  
[2025-05-29 01:46:01] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 01:46:01] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 01:46:06] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:46:06] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:46:07] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:46:06","message":"This is a test notification sent at 2025-05-29 01:46:06","data":{"notification_id":6,"category":"General","severity":"high","time":"2025-05-29 01:46:06"}}} 
[2025-05-29 01:46:07] local.ERROR: Call to a member function send() on null {"exception":"[object] (Error(code: 0): Call to a member function send() on null at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:154)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#1 {main}
"} 
[2025-05-29 01:46:52] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:46:52] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:46:52] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:46:52","message":"This is a test notification sent at 2025-05-29 01:46:52","data":{"notification_id":7,"category":"General","severity":"high","time":"2025-05-29 01:46:52"}}} 
[2025-05-29 01:46:52] local.ERROR: Call to a member function send() on null {"exception":"[object] (Error(code: 0): Call to a member function send() on null at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:154)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#1 {main}
"} 
[2025-05-29 01:47:50] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:47:50] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json"} 
[2025-05-29 01:47:50] local.ERROR: Firebase service account file not found at: C:\Users\<USER>\CAPSTONE\WebAlerto-1\storage\storage/firebase-service-account.json  
[2025-05-29 01:47:50] local.ERROR: FCM Test Error {"error":"Firebase service account file not found at: C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\storage\\storage/firebase-service-account.json","trace":"#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php(143): App\\Services\\FCMService->initializeFirebase()
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#2 {main}"} 
[2025-05-29 01:48:36] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 01:48:36] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:48:36","message":"This is a test notification sent at 2025-05-29 01:48:36","data":{"notification_id":9,"category":"General","severity":"high","time":"2025-05-29 01:48:36"}}} 
[2025-05-29 01:48:39] local.ERROR: App\Services\FCMService::logSuccess(): Argument #2 ($messageId) must be of type string, array given, called in C:\Users\<USER>\CAPSTONE\WebAlerto-1\app\Services\FCMService.php on line 160 {"exception":"[object] (TypeError(code: 0): App\\Services\\FCMService::logSuccess(): Argument #2 ($messageId) must be of type string, array given, called in C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php on line 160 at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:331)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php(160): App\\Services\\FCMService->logSuccess('fblRtHpkT3Wm4PV...', Array)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#2 {main}
"} 
[2025-05-29 01:53:01] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 01:53:01] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:53:01","message":"This is a test notification sent at 2025-05-29 01:53:01","data":{"notification_id":10,"category":"General","severity":"high","time":"2025-05-29 01:53:01"}}} 
[2025-05-29 01:53:03] local.ERROR: App\Services\FCMService::logSuccess(): Argument #2 ($messageId) must be of type string, array given, called in C:\Users\<USER>\CAPSTONE\WebAlerto-1\app\Services\FCMService.php on line 160 {"exception":"[object] (TypeError(code: 0): App\\Services\\FCMService::logSuccess(): Argument #2 ($messageId) must be of type string, array given, called in C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php on line 160 at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:331)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php(160): App\\Services\\FCMService->logSuccess('fblRtHpkT3Wm4PV...', Array)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#2 {main}
"} 
[2025-05-29 01:53:36] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 01:53:37] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:53:36","message":"This is a test notification sent at 2025-05-29 01:53:36","data":{"notification_id":11,"category":"General","severity":"high","time":"2025-05-29 01:53:36"}}} 
[2025-05-29 01:53:38] local.INFO: FCM notification sent successfully {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","message_id":"{\"name\":\"projects\\/last-5acaf\\/messages\\/0:1748483619479965%f6d21efbf9fd7ecd\"}"} 
[2025-05-29 01:53:38] local.ERROR: App\Services\FCMService::send(): Return value must be of type string, array returned {"exception":"[object] (TypeError(code: 0): App\\Services\\FCMService::send(): Return value must be of type string, array returned at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:161)
[stacktrace]
#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(106): App\\Services\\FCMService->send('fblRtHpkT3Wm4PV...', Array)
#1 {main}
"} 
[2025-05-29 01:53:59] local.ERROR: Declaration of App\Services\FCMService::send(string $token, array $notification) must be compatible with App\Contracts\FCMServiceInterface::send(string $token, array $notification): string {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Services\\FCMService::send(string $token, array $notification) must be compatible with App\\Contracts\\FCMServiceInterface::send(string $token, array $notification): string at C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\app\\Services\\FCMService.php:138)
[stacktrace]
#0 {main}
"} 
[2025-05-29 01:54:26] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 01:54:26] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:54:26","message":"This is a test notification sent at 2025-05-29 01:54:26","data":{"notification_id":12,"category":"General","severity":"high","time":"2025-05-29 01:54:26"}}} 
[2025-05-29 01:54:27] local.INFO: FCM notification sent successfully {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","message_id":"{\"name\":\"projects\\/last-5acaf\\/messages\\/0:1748483668407585%f6d21efbf9fd7ecd\"}"} 
[2025-05-29 01:54:27] local.ERROR: FCM Test Error {"error":"Array to string conversion","trace":"#0 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\CAP...', 118)
#1 C:\\Users\\<USER>\\CAPSTONE\\WebAlerto-1\\test-fcm-notification.php(118): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\CAP...', 118)
#2 {main}"} 
[2025-05-29 01:54:56] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 01:54:56] local.INFO: Sending FCM notification {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","notification":{"title":"Test Notification 01:54:56","message":"This is a test notification sent at 2025-05-29 01:54:56","data":{"notification_id":13,"category":"General","severity":"high","time":"2025-05-29 01:54:56"}}} 
[2025-05-29 01:54:58] local.INFO: FCM notification sent successfully {"token":"fblRtHpkT3Wm4PVXi0yBeZ:APA91bFW2OscIcP4kf07ExOl1qniCEHQViXApCGdHqIIXZVBC0Cgfe_2MD5FLBW4IVq1qJbKPkVXxRlF1AL3YYooH_xslOoATBSr8PAShvLkR5-cLR6ZOI4","message_id":"{\"name\":\"projects\\/last-5acaf\\/messages\\/0:****************%f6d21efbf9fd7ecd\"}"} 
[2025-05-29 02:19:54] local.INFO: API request received for evacuation centers  
[2025-05-29 02:19:54] local.INFO: Retrieved evacuation centers from database {"count":1} 
[2025-05-29 02:19:54] local.INFO: Returning formatted evacuation centers {"count":1} 
[2025-05-29 02:21:52] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:21:52] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:21:52] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:21:52] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:21:53] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:21:53] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:21:56] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:21:56] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:05] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:05] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:08] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:08] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:08] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:08] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:23:09] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:09] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:12] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:12] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:15] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:15] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:22] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:22] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:22] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:22] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:23:22] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:22] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:35] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:35] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:38] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:38] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:23:38] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:38] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:23:38] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:23:38] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:29:30] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:29:30] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:29:33] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:29:33] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:29:33] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:29:33] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:29:34] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:29:34] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:30:01] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:30:01] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:30:05] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:30:05] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:30:05] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:30:05] local.ERROR: Firebase service account file not found. Please check that the file exists at: storage/firebase-service-account.json  
[2025-05-29 02:30:06] local.ERROR: Firebase service account file not found at: storage/firebase-service-account.json  
[2025-05-29 02:30:06] local.ERROR: FCM initialization failed in constructor {"error":"Firebase service account file not found at: storage/firebase-service-account.json"} 
[2025-05-29 02:34:03] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:34:23] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:34:25] local.INFO: Notification process completed {"id":20,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:34:25] local.INFO: Test notification process completed {"id":20,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:34:26] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:35:07] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:35:09] local.INFO: Notification process completed {"id":21,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:35:09] local.INFO: Test notification process completed {"id":21,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:35:09] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:36:19] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:36:20] local.INFO: Notification process completed {"id":22,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:36:20] local.INFO: Test notification process completed {"id":22,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:36:21] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:36:40] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:36:42] local.INFO: Notification process completed {"id":23,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:36:42] local.INFO: Test notification process completed {"id":23,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:36:42] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:46:30] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:46:45] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-05-29 02:46:46] local.INFO: Notification process completed {"id":24,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:46:46] local.INFO: Test notification process completed {"id":24,"sent":true,"success_count":1,"failure_count":0,"invalid_tokens":0} 
[2025-05-29 02:46:47] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
