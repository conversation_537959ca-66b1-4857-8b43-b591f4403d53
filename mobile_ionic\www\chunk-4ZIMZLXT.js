import{a as d}from"./chunk-FULEFYAM.js";import{a as b}from"./chunk-AGHLTJ5J.js";import{$ as p,Cb as w,Da as y,b as f,ea as m,m as h,o as g,zb as k}from"./chunk-QCXYQNJC.js";import{a as u,g as n}from"./chunk-2R6CW7ES.js";var c=b("FirebaseMessaging",{web:()=>import("./chunk-5TZR3RXB.js").then(l=>new l.FirebaseMessagingWeb)});var G=(()=>{class l{constructor(e,o,t,r,i){this.http=e,this.platform=o,this.toastCtrl=t,this.alertCtrl=r,this.router=i,this.notificationSubject=new f,this.notifications$=this.notificationSubject.asObservable()}initPush(){return n(this,null,function*(){try{if(console.log("\u{1F525} Starting FCM initialization..."),this.platform.is("capacitor")){if(console.log("\u{1F4F1} Platform is Capacitor, proceeding with FCM setup..."),this.platform.is("android")){console.log("\u{1F916} Android platform detected, creating notification channels...");try{yield this.createAndroidNotificationChannels(),console.log("\u2705 Android notification channels created successfully")}catch(o){console.error("\u274C Failed to create Android notification channels:",o)}}if((yield this.checkGooglePlayServices())?localStorage.removeItem("google_play_services_missing"):(console.warn("Google Play Services not available. FCM may not work properly."),this.alertCtrl.create({header:"Google Play Services Required",message:"This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.",buttons:["OK"]}).then(o=>o.present()),localStorage.setItem("google_play_services_missing","true")),this.platform.is("capacitor"))try{let o=yield c.requestPermissions();console.log("FCM permission result:",o),o.receive==="granted"?(console.log("FCM permission granted"),console.log("Device registered with FCM")):(console.warn("FCM permission not granted:",o.receive),this.alertCtrl.create({header:"Notification Permission Required",message:"This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.",buttons:["OK"]}).then(t=>t.present()))}catch(o){console.error("Error initializing Capacitor Firebase Messaging:",o)}try{console.log("\u{1F3AB} Attempting to get FCM token...");let o=yield this.getToken();o?(console.log("\u2705 FCM Token retrieved successfully:",o.substring(0,20)+"..."),this.registerTokenWithBackend(o)):console.warn("\u26A0\uFE0F FCM token is empty or null")}catch(o){console.error("\u274C Error getting FCM token:",o)}try{this.platform.is("capacitor")&&c.addListener("tokenReceived",o=>{console.log("FCM Token refreshed (Capacitor):",o.token.substring(0,20)+"..."),this.registerTokenWithBackend(o.token)})}catch(o){console.error("Failed to set up token refresh:",o)}this.setupNotificationListeners()}else console.log("FCM not initialized: not running on a device")}catch(e){console.error("Error in initPush:",e)}})}getToken(){return n(this,null,function*(){if(this.platform.is("capacitor"))try{console.log("\u{1F3AB} Requesting FCM token from Capacitor Firebase Messaging...");let e=yield c.getToken();if(e&&e.token)return console.log("\u2705 Got FCM token from Capacitor Firebase Messaging:",e.token.substring(0,20)+"..."),e.token;throw console.error("\u274C FCM token result is empty or invalid:",e),new Error("FCM token is empty or invalid")}catch(e){throw console.error("\u274C Error getting token from Capacitor Firebase Messaging:",e),e}else{let e="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("\u{1F310} Using mock FCM token for browser:",e),Promise.resolve(e)}})}registerTokenWithBackend(e,o){if(localStorage.getItem("fcm_token")===e){console.log("Token already registered, skipping registration");return}let r="web";this.platform.is("ios")?r="ios":this.platform.is("android")&&(r="android"),console.log(`Registering ${r} token with backend...`);let i={token:e,device_type:r,project_id:d.firebase.projectId||"last-5acaf"};if(o)i.user_id=o,console.log(`Associating token with user ID: ${o}`);else{let s=localStorage.getItem("token");if(s)try{let a=this.parseJwt(s);a&&a.sub&&(i.user_id=a.sub,console.log(`Associating token with user ID from JWT: ${a.sub}`))}catch(a){console.error("Error parsing JWT token:",a)}}e&&(d.firebase.projectId||i.project_id)?(localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registering","true"),this.http.post(`${d.apiUrl}/device-token`,i).subscribe({next:s=>{console.log("Token registered with backend:",s),localStorage.removeItem("fcm_token_registering")},error:s=>{console.error("Error registering token:",s),localStorage.removeItem("fcm_token_registering")}})):(console.log("Skipping token registration: Missing project ID or token"),e&&localStorage.setItem("fcm_token",e))}parseJwt(e){try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),r=decodeURIComponent(atob(t).split("").map(function(i){return"%"+("00"+i.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(r)}catch(o){return console.error("Error parsing JWT token:",o),null}}setupNotificationListeners(){this.setupCapacitorNotificationListeners()}setupCapacitorNotificationListeners(){try{this.platform.is("capacitor")&&(console.log("Setting up Capacitor Firebase Messaging notification listeners"),c.addListener("notificationReceived",e=>{console.log("Capacitor: Notification received in foreground:",e);let o=e.notification.data||{};this.processNotification(u({title:e.notification.title||"",body:e.notification.body||"",category:o.category||"",severity:o.severity||"low",wasTapped:!1,notification_id:o.notification_id||null,time:new Date().toISOString()},Object.keys(o).filter(t=>!["category","severity","notification_id"].includes(t)).reduce((t,r)=>(t[r]=o[r],t),{})))}),c.addListener("notificationActionPerformed",e=>{console.log("Capacitor: Notification tapped:",e);let o=e.notification.data||{};this.processNotification(u({title:e.notification.title||"",body:e.notification.body||"",category:o.category||"",severity:o.severity||"low",wasTapped:!0,notification_id:o.notification_id||null,time:new Date().toISOString()},Object.keys(o).filter(t=>!["category","severity","notification_id"].includes(t)).reduce((t,r)=>(t[r]=o[r],t),{})))}))}catch(e){console.error("Failed to set up Capacitor notification listeners:",e)}}processNotification(e){try{console.log("Processed notification:",{title:e.title,body:e.body,category:e.category,severity:e.severity,wasTapped:e.wasTapped,notification_id:e.notification_id,project_id:d.firebase.projectId||"new-firebase-project"}),this.notificationSubject.next(e),e.wasTapped?(console.log("Notification tapped in background"),this.handleBackgroundNotification(e)):(console.log("Notification received in foreground"),this.handleForegroundNotification(e))}catch(o){console.error("Error processing notification:",o,e)}}handleForegroundNotification(e){return n(this,null,function*(){try{console.log("\u{1F514} Handling foreground notification:",e),this.vibrateDevice(),this.playNotificationSound(),yield new Promise(o=>setTimeout(o,500)),yield this.showEmergencyNotificationModal(e)}catch(o){console.error("\u274C Error handling foreground notification:",o),yield this.showFallbackToast(e)}})}showEmergencyNotificationModal(e){return n(this,null,function*(){try{console.log("\u{1F4F1} Creating emergency notification modal...",e),yield new Promise(r=>setTimeout(r,100));let o=yield this.alertCtrl.getTop();o&&(console.log("\u26A0\uFE0F Alert already present, dismissing first..."),yield o.dismiss(),yield new Promise(r=>setTimeout(r,500))),console.log("\u{1F35E} Showing immediate toast notification as backup..."),yield this.showFallbackToast(e);let t=yield this.alertCtrl.create({header:e.title||"EMERGENCY ALERT",subHeader:e.category?`${e.category.toUpperCase()} ALERT`:"",message:e.body||"",buttons:[{text:"Go to Safe Area",cssClass:"alert-button-primary",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from modal"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",cssClass:"alert-button-secondary",handler:()=>(console.log("\u274C User dismissed notification modal"),!0)}],cssClass:`emergency-notification ${e.category?.toLowerCase()||"general"}-alert`,backdropDismiss:!1,keyboardClose:!1});console.log("\u2705 Emergency modal created, presenting..."),yield t.present(),console.log("\u2705 Emergency modal presented successfully")}catch(o){console.error("\u274C Error creating emergency modal:",o),console.error("\u274C Modal error details:",o?.message,o?.stack),yield this.showFallbackToast(e)}})}showFallbackToast(e){return n(this,null,function*(){try{console.log("\u{1F35E} Showing emergency toast notification"),yield(yield this.toastCtrl.create({header:`\u{1F6A8} ${e.title||"EMERGENCY ALERT"}`,message:e.body||"Emergency notification received",duration:8e3,position:"top",color:this.getToastColor(e.category),cssClass:"emergency-toast",buttons:[{text:"\u{1F5FA}\uFE0F Go to Safe Area",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from toast"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",handler:()=>(console.log("\u274C User dismissed toast notification"),!0)}]})).present(),console.log("\u2705 Emergency toast shown successfully"),this.vibrateDevice()}catch(o){console.error("\u274C Even fallback toast failed:",o);try{yield(yield this.alertCtrl.create({header:"\u{1F6A8} EMERGENCY ALERT",message:`${e.title}

${e.body}`,buttons:[{text:"Go to Safe Area",handler:()=>this.navigateBasedOnNotification(e)},"Dismiss"]})).present(),console.log("\u2705 Simple alert shown as last resort")}catch(t){console.error("\u274C All notification methods failed:",t),console.log("\u{1F4E2} EMERGENCY NOTIFICATION (all display methods failed):",e)}}})}getToastColor(e){if(!e)return"warning";switch(e.toLowerCase()){case"earthquake":return"warning";case"flood":return"primary";case"typhoon":return"success";case"fire":return"danger";default:return"warning"}}simulateForegroundNotification(e){return n(this,null,function*(){console.log("\u{1F9EA} Simulating foreground notification:",e),yield this.processNotification(e)})}simulateBackgroundNotification(e){return n(this,null,function*(){console.log("\u{1F9EA} Simulating background notification:",e),e.wasTapped=!0,yield this.processNotification(e)})}getDisasterStyle(e){if(!e)return{color:"#666666",icon:"notifications-outline"};let o=e.toLowerCase();return o.includes("earthquake")||o.includes("quake")?{color:"#ffa500",icon:"earth-outline"}:o.includes("flood")||o.includes("flash")?{color:"#0000ff",icon:"water-outline"}:o.includes("typhoon")||o.includes("storm")||o.includes("hurricane")?{color:"#008000",icon:"thunderstorm-outline"}:o.includes("fire")?{color:"#ff0000",icon:"flame-outline"}:{color:"#666666",icon:"alert-circle-outline"}}vibrateDevice(){"vibrate"in navigator?(navigator.vibrate([1e3,200,1e3,200,1e3]),console.log("Device vibration triggered with strong pattern")):console.log("Vibration API not supported on this device")}playNotificationSound(){try{let e=new Audio;e.src="data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...",e.volume=1,e.play().catch(o=>{console.error("Error playing notification sound:",o)})}catch(e){console.error("Error creating audio element:",e)}}handleBackgroundNotification(e){return n(this,null,function*(){try{console.log("\u{1F514} Handling background notification tap:",e),this.vibrateDevice(),yield new Promise(o=>setTimeout(o,1e3)),yield this.showEmergencyNotificationModal(e)}catch(o){console.error("\u274C Error handling background notification:",o),this.navigateBasedOnNotification(e)}})}navigateBasedOnNotification(e){if(console.log("\u{1F5FA}\uFE0F Navigating based on notification:",e),e.category==="evacuation_center"||e.data&&e.data.type==="evacuation_center_added"){console.log("\u{1F3E2} New evacuation center notification detected"),this.handleEvacuationCenterNotification(e);return}if(e.category){let o=e.category.toLowerCase();console.log("\u{1F4CD} Notification category:",o);let t="";switch(o){case"flood":case"flashflood":t="Flood";break;case"earthquake":case"quake":t="Earthquake";break;case"typhoon":case"storm":case"hurricane":t="Typhoon";break;case"fire":t="Fire";break;default:console.warn("Unknown disaster category:",o),t="all";break}if(console.log(`\u{1F5FA}\uFE0F Mapped disaster type: ${o} -> ${t}`),t&&t!=="all"){console.log("\u{1F5FA}\uFE0F Navigating to disaster-specific map:",t);let r="";switch(t.toLowerCase()){case"earthquake":r="/earthquake-map";break;case"typhoon":r="/typhoon-map";break;case"flood":r="/flood-map";break;default:r="/tabs/map";break}console.log(`\u{1F5FA}\uFE0F Navigating to route: ${r}`),this.router.navigate([r])}else console.log("\u{1F3E0} Navigating to home (unknown disaster type)"),this.router.navigate(["/tabs/home"])}else console.log("\u{1F3E0} Navigating to home (no category)"),this.router.navigate(["/tabs/home"])}handleEvacuationCenterNotification(e){console.log("\u{1F3E2} Handling evacuation center notification:",e);try{let o=null;if(e.data&&(o=e.data),o&&o.evacuation_center_id){let t=o.evacuation_center_id,r=o.disaster_type,i=o.location?.latitude,s=o.location?.longitude;console.log("\u{1F3E2} Evacuation center details:",{id:t,disasterType:r,lat:i,lng:s});let a="/all-maps";if(r)switch(r.toLowerCase()){case"earthquake":a="/earthquake-map";break;case"typhoon":a="/typhoon-map";break;case"flood":case"flash flood":a="/flood-map";break;default:a="/all-maps";break}console.log(`\u{1F5FA}\uFE0F Navigating to ${a} for new evacuation center`),this.router.navigate([a],{queryParams:{newCenterId:t,highlightCenter:"true",centerLat:i,centerLng:s}})}else console.log("\u{1F3E2} No evacuation center data found, navigating to all maps"),this.router.navigate(["/all-maps"])}catch(o){console.error("\u274C Error handling evacuation center notification:",o),this.router.navigate(["/all-maps"])}}checkGooglePlayServices(){return n(this,null,function*(){try{if(this.platform.is("capacitor")&&this.platform.is("android"))try{return yield c.getToken(),!0}catch(e){console.error("Error checking Google Play Services:",e);let o=e.message||"";return!(o.includes("Google Play Services")||o.includes("GoogleApiAvailability")||o.includes("API unavailable"))}return!0}catch(e){return console.error("Error in checkGooglePlayServices:",e),!0}})}createAndroidNotificationChannels(){return n(this,null,function*(){try{this.platform.is("android")&&(console.log("Creating Android notification channels"),yield this.sendTestChannelNotification("emergency-alerts","Emergency Alerts","High priority notifications for emergencies","high"),yield this.sendTestChannelNotification("general-notifications","General Notifications","Standard notifications","default"),console.log("Android notification channels created successfully"))}catch(e){console.error("Error creating Android notification channels:",e)}})}sendTestChannelNotification(e,o,t,r){return n(this,null,function*(){try{let i={notification:{title:"Channel Setup",body:"Setting up notification channels",android:{channelId:e,priority:r==="high"?"high":r==="default"?"default":"low",sound:r!=="low",vibrate:r!=="low",visibility:"public"}}};console.log(`Created notification channel: ${e} (${o})`)}catch(i){console.error(`Error creating notification channel ${e}:`,i)}})}refreshFCMToken(e){return n(this,null,function*(){try{if(console.log("Refreshing FCM token..."),this.platform.is("capacitor"))try{yield c.deleteToken(),console.log("Existing FCM token deleted");let o=yield c.getToken();return console.log("New FCM token obtained:",o.token),this.registerTokenWithBackend(o.token,e),!0}catch(o){return console.error("Error refreshing Capacitor FCM token:",o),!1}else{let o="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("New mock FCM token generated:",o),this.registerTokenWithBackend(o,e),!0}}catch(o){return console.error("Error in refreshFCMToken:",o),!1}})}static{this.\u0275fac=function(o){return new(o||l)(g(p),g(y),g(w),g(k),g(m))}}static{this.\u0275prov=h({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})();export{G as a};
