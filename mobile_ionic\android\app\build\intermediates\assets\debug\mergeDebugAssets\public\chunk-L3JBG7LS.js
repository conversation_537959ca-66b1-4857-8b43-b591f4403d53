import{a as se}from"./chunk-P7IGRJGJ.js";import{a as ae,b as re,c as he}from"./chunk-LEWYLK2X.js";import{a as w}from"./chunk-EOTJZDZP.js";import{a as oe}from"./chunk-4ZIMZLXT.js";import{a as ie}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as T,$a as Q,A as P,Ab as ne,C as v,Cb as B,D as g,Db as L,F as a,G as l,H as h,I as S,J as m,K as _,M as d,Ma as U,N as D,Na as E,O as F,Oa as G,Pa as J,Qa as K,U as V,W as k,Wa as W,X as I,ab as R,ea as q,ib as X,j as z,m as A,o as M,q as x,r as C,tb as Y,u as j,ub as Z,vb as ee,y as p,z as u,zb as te}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{a as H,b as $,f as ge,g as f}from"./chunk-2R6CW7ES.js";var fe=ge(he());var de=(()=>{class o{constructor(e,t,n){this.http=e,this.offlineStorage=t,this.mapboxRouting=n}getRoute(e,t,n,i,r="walking"){return f(this,null,function*(){let s=yield this.offlineStorage.getRoute(e,t,n,i,r);if(s)return console.log("\u{1F4CD} Using cached route"),{coordinates:JSON.parse(s.route_data),distance:s.distance,duration:s.duration,isOffline:!0,travelMode:s.travel_mode};if(this.offlineStorage.isOnline()&&!this.offlineStorage.isOfflineMode())try{let c=yield this.getOnlineRoute(e,t,n,i,r);if(c)return yield this.cacheRoute(e,t,n,i,c,r),$(H({},c),{isOffline:!1})}catch(c){console.warn("\u26A0\uFE0F Online routing failed, falling back to offline:",c)}return console.log("\u26A0\uFE0F Offline mode: No routing available. Show evacuation centers only."),null})}getOnlineRoute(e,t,n,i,r){return f(this,null,function*(){try{let s=yield this.mapboxRouting.getDirections(t,e,i,n,r);if(s.routes&&s.routes.length>0){let c=s.routes[0];return{coordinates:c.geometry.coordinates.map(b=>[b[1],b[0]]),distance:c.distance,duration:c.duration,isOffline:!1,travelMode:r}}}catch(s){console.error("\u274C Mapbox routing error:",s)}return null})}cacheRoute(e,t,n,i,r,s){return f(this,null,function*(){let c={start_lat:e,start_lng:t,end_lat:n,end_lng:i,disaster_type:"general",route_data:JSON.stringify(r.coordinates),distance:r.distance,duration:r.duration,travel_mode:s};yield this.offlineStorage.saveRoute(c)})}generateOfflineRoute(e,t,n,i,r){return console.log("\u26A0\uFE0F No routing available in offline mode - showing distance only"),null}calculateDistance(e,t,n,i){let s=this.toRadians(n-e),c=this.toRadians(i-t),O=Math.sin(s/2)*Math.sin(s/2)+Math.cos(this.toRadians(e))*Math.cos(this.toRadians(n))*Math.sin(c/2)*Math.sin(c/2);return 6371*(2*Math.atan2(Math.sqrt(O),Math.sqrt(1-O)))}toRadians(e){return e*(Math.PI/180)}estimateDuration(e,t){let n=e/1e3,i={walking:5,cycling:15,driving:40},r=i[t]||i.walking;return n/r*3600}addRouteToMap(e,t,n="#007bff",i=4){let r=fe.polyline(t.coordinates,{color:n,weight:i,opacity:t.isOffline?.7:1,dashArray:t.isOffline?"10, 5":void 0}).addTo(e),s=(t.distance/1e3).toFixed(1),c=Math.round(t.duration/60),O=t.isOffline?"Offline Route":"Online Route";return r.bindPopup(`
      <div class="route-popup">
        <strong>${O}</strong><br>
        Distance: ${s} km<br>
        Duration: ${c} min<br>
        Mode: ${t.travelMode}
      </div>
    `),r}preCacheRoutes(r,s,c){return f(this,arguments,function*(e,t,n,i=["walking","cycling"]){if(!this.offlineStorage.isOnline()){console.log("\u26A0\uFE0F Cannot pre-cache routes while offline");return}console.log("\u{1F504} Pre-caching routes for evacuation centers...");let O=0;for(let b of n)for(let ue of i)try{yield this.getRoute(e,t,b.latitude,b.longitude,ue),O++,yield new Promise(N=>setTimeout(N,500))}catch(N){console.warn(`Failed to cache route to ${b.name}:`,N)}console.log(`\u2705 Pre-cached ${O} routes`)})}static{this.\u0275fac=function(t){return new(t||o)(M(T),M(w),M(re))}}static{this.\u0275prov=A({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();function we(o,y){if(o&1){let e=S();a(0,"ion-button",9),m("click",function(){x(e);let n=_();return C(n.enableOfflineMode())}),d(1," Continue Offline "),l()}}function ye(o,y){if(o&1){let e=S();a(0,"ion-button",9),m("click",function(){x(e);let n=_();return C(n.syncData())}),h(1,"ion-icon",10),d(2," Sync "),l()}}function xe(o,y){if(o&1){let e=S();a(0,"ion-button",9),m("click",function(){x(e);let n=_();return C(n.prepareOfflineData())}),h(1,"ion-icon",11),d(2," Prepare "),l()}}function Ce(o,y){if(o&1){let e=S();a(0,"ion-button",9),m("click",function(){x(e);let n=_();return C(n.clearOfflineData())}),h(1,"ion-icon",12),d(2," Clear "),l()}}function ve(o,y){if(o&1&&(a(0,"div",13),h(1,"ion-progress-bar",14),a(2,"div",15),d(3),l()()),o&2){let e=_();p(),g("value",e.preparationProgress),p(2),D(e.preparationStatus)}}var pe=(()=>{class o{constructor(e,t,n,i,r,s){this.offlineStorage=e,this.offlineMap=t,this.offlineRouting=n,this.alertCtrl=i,this.loadingCtrl=r,this.toastCtrl=s,this.offlineModeEnabled=new j,this.dataSynced=new j,this.isOnline=navigator.onLine,this.isOfflineMode=!1,this.hasOfflineData=!1,this.isPreparingData=!1,this.preparationProgress=0,this.preparationStatus="",this.lastSyncTime=null}ngOnInit(){return f(this,null,function*(){this.onlineListener=()=>{console.log("\u{1F310} Network online event detected"),this.isOnline=!0,this.checkDataStatus()},this.offlineListener=()=>{console.log("\u{1F4E1} Network offline event detected"),this.isOnline=!1,this.checkDataStatus()},window.addEventListener("online",this.onlineListener),window.addEventListener("offline",this.offlineListener),this.isOnline=navigator.onLine,console.log("\u{1F50D} Initial network status:",this.isOnline),yield this.checkDataStatus()})}ngOnDestroy(){this.onlineListener&&window.removeEventListener("online",this.onlineListener),this.offlineListener&&window.removeEventListener("offline",this.offlineListener)}checkDataStatus(){return f(this,null,function*(){this.isOfflineMode=this.offlineStorage.isOfflineMode(),this.hasOfflineData=yield this.offlineStorage.isDataAvailable(),this.lastSyncTime=this.offlineStorage.getLastSyncTime()})}getBannerClass(){return this.isPreparingData?"preparing":this.isOnline?this.isOnline&&!this.hasOfflineData?"warning":"online":"offline"}getBannerIcon(){return this.isPreparingData?"download-outline":this.isOnline?this.isOnline&&!this.hasOfflineData?"warning-outline":"checkmark-circle-outline":"wifi-outline"}getBannerTitle(){return this.isPreparingData?"Preparing Offline Data":!this.isOnline&&this.hasOfflineData?"Offline Mode Available":!this.isOnline&&!this.hasOfflineData?"No Internet Connection":this.isOnline&&!this.hasOfflineData?"Offline Data Not Ready":"Connected & Ready"}getBannerSubtitle(){return this.isPreparingData?this.preparationStatus:!this.isOnline&&this.hasOfflineData?"Emergency data is available offline":!this.isOnline&&!this.hasOfflineData?"Limited functionality available":this.isOnline&&!this.hasOfflineData?"Prepare offline data for emergencies":this.lastSyncTime?`Last synced: ${new Date(this.lastSyncTime).toLocaleDateString()}`:"All systems operational"}showOfflineButton(){return!this.isOnline&&!this.isOfflineMode&&this.hasOfflineData}showSyncButton(){return this.isOnline&&this.hasOfflineData&&!this.isPreparingData}showPrepareButton(){return this.isOnline&&!this.hasOfflineData&&!this.isPreparingData}showClearButton(){return this.isOnline&&this.hasOfflineData&&!this.isPreparingData}enableOfflineMode(){return f(this,null,function*(){yield(yield this.alertCtrl.create({header:"Enable Offline Mode",message:"Switch to offline mode to access cached evacuation data and maps?",buttons:[{text:"Cancel",role:"cancel"},{text:"Continue Offline",handler:()=>{this.offlineStorage.setOfflineMode(!0),this.isOfflineMode=!0,this.offlineModeEnabled.emit(),this.showToast("Offline mode enabled. Using cached data.","success")}}]})).present()})}syncData(){return f(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Syncing evacuation data..."});yield e.present();try{let t=yield this.offlineStorage.syncEvacuationCenters();yield e.dismiss(),t?(this.dataSynced.emit(),this.checkDataStatus(),this.showToast("Data synced successfully","success")):this.showToast("Sync failed. Please try again.","danger")}catch{yield e.dismiss(),this.showToast("Sync error. Check your connection.","danger")}})}clearOfflineData(){return f(this,null,function*(){yield(yield this.alertCtrl.create({header:"Clear Offline Data",message:"This will delete all cached evacuation centers, routes, and map tiles. Are you sure?",buttons:[{text:"Cancel",role:"cancel"},{text:"Clear All",role:"destructive",handler:()=>f(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Clearing offline data..."});yield t.present();try{yield this.offlineStorage.clearAllOfflineData(),yield t.dismiss(),this.checkDataStatus(),this.showToast("Offline data cleared successfully","success")}catch{yield t.dismiss(),this.showToast("Error clearing data. Please try again.","danger")}})}]})).present()})}prepareOfflineData(){return f(this,null,function*(){yield(yield this.alertCtrl.create({header:"Prepare Offline Data",message:"Download evacuation centers and map data for offline use? This may take a few minutes and use mobile data.",buttons:[{text:"Cancel",role:"cancel"},{text:"Download",handler:()=>this.startDataPreparation()}]})).present()})}startDataPreparation(){return f(this,null,function*(){this.isPreparingData=!0,this.preparationProgress=0;try{this.preparationStatus="Downloading evacuation centers...";let e=yield this.offlineStorage.syncEvacuationCenters();if(this.preparationProgress=.3,!e)throw new Error("Failed to sync evacuation centers");this.preparationStatus="Getting your location...";let t=yield ae.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4});this.preparationProgress=.4;let n=t.coords.latitude,i=t.coords.longitude;this.preparationStatus="Downloading map tiles...",yield this.offlineMap.preloadMapTiles(n,i,25,(s,c)=>{let O=.4+s/c*.4;this.preparationProgress=O,this.preparationStatus=`Downloading map tiles... ${s}/${c}`}),this.preparationStatus="Pre-caching routes...";let r=yield this.offlineStorage.getEvacuationCenters();yield this.offlineRouting.preCacheRoutes(n,i,r.slice(0,10)),this.preparationProgress=1,this.preparationStatus="Preparation complete!",yield this.checkDataStatus(),setTimeout(()=>{this.isPreparingData=!1,this.showToast("Offline data prepared successfully!","success")},1e3)}catch(e){console.error("Data preparation failed:",e),this.isPreparingData=!1,this.showToast("Failed to prepare offline data. Please try again.","danger")}})}showToast(e,t){return f(this,null,function*(){yield(yield this.toastCtrl.create({message:e,duration:3e3,color:t,position:"bottom"})).present()})}static{this.\u0275fac=function(t){return new(t||o)(u(w),u(se),u(de),u(te),u(ne),u(B))}}static{this.\u0275cmp=P({type:o,selectors:[["app-offline-banner"]],outputs:{offlineModeEnabled:"offlineModeEnabled",dataSynced:"dataSynced"},decls:14,vars:9,consts:[[1,"offline-banner",3,"ngClass"],[1,"banner-content"],[1,"banner-icon",3,"name"],[1,"banner-text"],[1,"banner-title"],[1,"banner-subtitle"],[1,"banner-actions"],["fill","clear","size","small","color","light",3,"click",4,"ngIf"],["class","preparation-progress",4,"ngIf"],["fill","clear","size","small","color","light",3,"click"],["name","sync-outline"],["name","download-outline"],["name","trash-outline"],[1,"preparation-progress"],[3,"value"],[1,"progress-text"]],template:function(t,n){t&1&&(a(0,"div",0)(1,"div",1),h(2,"ion-icon",2),a(3,"div",3)(4,"div",4),d(5),l(),a(6,"div",5),d(7),l()(),a(8,"div",6),v(9,we,2,0,"ion-button",7)(10,ye,3,0,"ion-button",7)(11,xe,3,0,"ion-button",7)(12,Ce,3,0,"ion-button",7),l()(),v(13,ve,4,2,"div",8),l()),t&2&&(g("ngClass",n.getBannerClass()),p(2),g("name",n.getBannerIcon()),p(3),D(n.getBannerTitle()),p(2),D(n.getBannerSubtitle()),p(2),g("ngIf",n.showOfflineButton()),p(),g("ngIf",n.showSyncButton()),p(),g("ngIf",n.showPrepareButton()),p(),g("ngIf",n.showClearButton()),p(),g("ngIf",n.isPreparingData))},dependencies:[L,E,R,X,I,V,k],styles:[".offline-banner[_ngcontent-%COMP%]{padding:12px 16px;margin:8px 16px;border-radius:8px;transition:all .3s ease}.offline-banner.online[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff}.offline-banner.offline[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#fd7e14);color:#fff}.offline-banner.preparing[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#6610f2);color:#fff}.offline-banner.warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#fd7e14);color:#212529}.banner-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.banner-icon[_ngcontent-%COMP%]{font-size:24px;flex-shrink:0}.banner-text[_ngcontent-%COMP%]{flex:1}.banner-title[_ngcontent-%COMP%]{font-weight:600;font-size:14px;margin-bottom:2px}.banner-subtitle[_ngcontent-%COMP%]{font-size:12px;opacity:.9}.banner-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.preparation-progress[_ngcontent-%COMP%]{margin-top:12px}.progress-text[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:4px;opacity:.9}ion-progress-bar[_ngcontent-%COMP%]{height:4px;border-radius:2px}"]})}}return o})();function Se(o,y){if(o&1&&(a(0,"ion-badge",21),d(1),l()),o&2){let e=_();p(),F(" ",e.unreadNotificationCount>99?"99+":e.unreadNotificationCount," ")}}var qe=(()=>{class o{constructor(e,t,n,i,r){this.router=e,this.toastCtrl=t,this.fcmService=n,this.http=i,this.offlineStorage=r,this.isOffline=!1,this.unreadNotificationCount=0,this.notificationSubscription=null,this.pollSubscription=null}ngOnInit(){let e=localStorage.getItem("isOffline");this.isOffline=e==="true",this.loadUnreadCount(),this.pollSubscription=z(3e4).subscribe(()=>{this.loadUnreadCount()}),this.notificationSubscription=this.fcmService.notifications$.subscribe(()=>{this.loadUnreadCount()})}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.pollSubscription&&this.pollSubscription.unsubscribe()}toggleStatus(){this.isOffline=!this.isOffline,localStorage.setItem("isOffline",String(this.isOffline))}openDisasterMap(e){console.log(`\u{1F3E0} HOME: Opening disaster-specific map for: ${e}`);let t=e,n="";e==="earthquake"?(t="Earthquake",n="/earthquake-map"):e==="typhoon"?(t="Typhoon",n="/typhoon-map"):e==="flashflood"&&(t="Flash Flood",n="/flood-map"),console.log(`\u{1F3E0} HOME: Navigating to ${n} for ${t}`),this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Opening ${t} evacuation centers...`,duration:2e3,color:"primary"}).then(i=>i.present()),this.router.navigate([n])}viewMap(){console.log("\u{1F3E0} HOME: Opening complete evacuation centers map"),this.toastCtrl.create({message:"\u{1F5FA}\uFE0F Opening complete evacuation centers map...",duration:2e3,color:"secondary"}).then(e=>e.present()),this.router.navigate(["/all-maps"])}loadUnreadCount(){return f(this,null,function*(){try{let e=yield this.http.get(`${ie.apiUrl}/notifications/unread-count`).toPromise();e&&(this.unreadNotificationCount=e.unread_count)}catch(e){console.error("Error loading unread notification count:",e)}})}openNotifications(){this.router.navigate(["/notifications"])}openDataDebug(){console.log("\u{1F41B} Opening data debug page"),this.router.navigate(["/data-debug"])}onOfflineModeEnabled(){console.log("\u{1F504} Offline mode enabled from banner"),this.isOffline=!0,this.showToast("Offline mode enabled. Using cached data.","success")}onDataSynced(){console.log("\u{1F504} Data synced from banner"),this.showToast("Evacuation data updated successfully","success")}showToast(e,t){return f(this,null,function*(){yield(yield this.toastCtrl.create({message:e,duration:3e3,color:t,position:"bottom"})).present()})}static{this.\u0275fac=function(t){return new(t||o)(u(q),u(B),u(oe),u(T),u(w))}}static{this.\u0275cmp=P({type:o,selectors:[["app-home"]],decls:40,vars:3,consts:[[3,"translucent"],["slot","end"],[1,"notification-button",3,"click"],["name","notifications-outline"],["class","notification-badge",4,"ngIf"],[3,"offlineModeEnabled","dataSynced"],[1,"ion-padding"],[1,"disaster-container"],[1,"home-title"],["src","assets/ALERTO.png","alt","App Logo",1,"home-logo"],[2,"font-size","22px"],[2,"font-size","35px","color","#1565c0","margin-top","0px"],[1,"top-disaster"],[1,"disaster","earthquake",3,"click"],["src","assets/earthquake.png","alt","Earthquake"],[1,"disaster","typhoon",3,"click"],["src","assets/typhoon.png","alt","Typhoon"],[1,"disaster","flood",3,"click"],["src","assets/flood.png","alt","Flood"],["expand","block",1,"view-map",2,"margin-top","24px","width","80%","height","45px","--border-radius","25px",3,"click","disabled"],["name","map","slot","start"],[1,"notification-badge"]],template:function(t,n){t&1&&(a(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),d(3," Alerto "),l(),a(4,"ion-buttons",1)(5,"ion-button",2),m("click",function(){return n.openNotifications()}),h(6,"ion-icon",3),v(7,Se,2,1,"ion-badge",4),l()()()(),a(8,"ion-content")(9,"app-offline-banner",5),m("offlineModeEnabled",function(){return n.onOfflineModeEnabled()})("dataSynced",function(){return n.onDataSynced()}),l(),a(10,"div",6)(11,"div",7)(12,"div",8),h(13,"img",9),a(14,"div",10),d(15,"Hi, Welcome to "),a(16,"p",11),d(17,"Safe Area!"),l()()(),a(18,"div",12)(19,"ion-card",13),m("click",function(){return n.openDisasterMap("earthquake")}),a(20,"ion-card-content"),h(21,"img",14),a(22,"ion-text")(23,"u"),d(24,"Earthquake"),l()()()(),a(25,"ion-card",15),m("click",function(){return n.openDisasterMap("typhoon")}),a(26,"ion-card-content"),h(27,"img",16),a(28,"ion-text")(29,"u"),d(30,"Typhoon"),l()()()(),a(31,"ion-card",17),m("click",function(){return n.openDisasterMap("flashflood")}),a(32,"ion-card-content"),h(33,"img",18),a(34,"ion-text")(35,"u"),d(36,"Flash Flood"),l()()()()(),a(37,"ion-button",19),m("click",function(){return n.viewMap()}),h(38,"ion-icon",20),d(39," See the Whole Map "),l()()()()),t&2&&(g("translucent",!0),p(7),g("ngIf",n.unreadNotificationCount>0),p(30),g("disabled",n.isOffline))},dependencies:[L,U,E,G,J,K,W,Q,R,Y,Z,ee,I,k,pe],styles:[".status-text[_ngcontent-%COMP%]{margin-left:8px}ion-header[_ngcontent-%COMP%], ion-title[_ngcontent-%COMP%]{text-align:center;font-family:Poppins,Arial,sans-serif;font-size:2rem;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.disaster-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:24px;margin:32px 0 0}.disaster[_ngcontent-%COMP%]{margin:0;cursor:pointer;transition:transform .2s}.disaster[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;padding:16px}.disaster[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;margin-bottom:8px}.earthquake[_ngcontent-%COMP%]{--background: #ffcc80}.typhoon[_ngcontent-%COMP%]{--background: #c5e1a5;size:100px;width:105px;height:120px}.flood[_ngcontent-%COMP%]{--background: #81d4fa}.view-map[_ngcontent-%COMP%]{margin-top:24px;--background: #00bfff}.view-map[_ngcontent-%COMP%]:hover{--background: #0090cc}.view-map[disabled][_ngcontent-%COMP%]{--background: #999}.top-disaster[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:16px;align-items:center}.home-logo[_ngcontent-%COMP%]{width:150px;height:150px}.home-title[_ngcontent-%COMP%]{padding-top:105px;display:flex;align-items:center;justify-content:center;font-size:30px;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.notifications-section[_ngcontent-%COMP%]{margin-top:20px;border-top:1px solid var(--ion-color-light);padding-top:10px}ion-item-divider[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-primary);font-weight:700;font-size:1.1rem;letter-spacing:.5px;margin-bottom:8px}.notification-button[_ngcontent-%COMP%]{position:relative}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#e41e3f;color:#fff;font-size:10px;font-weight:600;min-width:16px;height:16px;border-radius:8px;display:flex;align-items:center;justify-content:center;z-index:10}"]})}}return o})();export{qe as HomePage};
