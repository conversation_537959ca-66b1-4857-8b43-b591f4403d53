# 🚨 FCM Notification Debugging Guide

## Issue: <PERSON>min can send notifications but mobile device can't receive them

## 🔍 Step-by-Step Debugging Process

### Phase 1: Check Basic FCM Setup ✅

1. **Verify Firebase Project Configuration**
   - ✅ Project ID: `last-5acaf` 
   - ✅ Package name: `io.ionic.starter`
   - ✅ google-services.json is in correct locations

2. **Check Android Permissions**
   - ✅ POST_NOTIFICATIONS permission in AndroidManifest.xml
   - ✅ WAKE_LOCK, VIBRATE permissions present

### Phase 2: Device-Level Debugging 📱

#### A. Check Device Notification Settings
1. **Android Settings > Apps > Alerto**
   - Notifications: Should be ON
   - All notification categories: Should be enabled
   - Battery optimization: Should be OFF for Alerto

2. **Check Google Play Services**
   - Settings > Apps > Google Play Services
   - Make sure it's updated and enabled

#### B. Check FCM Token Registration
1. **Open Chrome DevTools** (connect device via USB)
   - Go to `chrome://inspect`
   - Select your device and app
   - Check console for FCM logs

2. **Look for these logs:**
   ```
   ✅ FCM Token retrieved successfully: [token...]
   Token registered with backend: [response]
   ```

3. **If token registration fails:**
   - Check network connectivity
   - Verify backend is running on correct IP
   - Check if device can reach: `http://***************:8000/api/device-token`

### Phase 3: Backend Verification 🖥️

#### A. Check Laravel Backend
1. **Verify FCM Service Account**
   - File exists: `WebAlerto-1/storage/firebase-service-account.json`
   - Contains correct project credentials

2. **Check Environment Variables**
   ```bash
   # In WebAlerto-1 directory
   php artisan config:clear
   php artisan config:cache
   ```

3. **Test Backend FCM Sending**
   - Use admin panel to send test notification
   - Check Laravel logs for errors: `storage/logs/laravel.log`

#### B. Database Check
1. **Verify Token Storage**
   ```sql
   SELECT * FROM device_tokens WHERE user_id = [your_user_id];
   ```
   - Should show your FCM token
   - Platform should be 'android'

### Phase 4: Network Debugging 🌐

#### A. Check API Connectivity
1. **Test from mobile device browser:**
   - Open: `http://***************:8000/api/device-token`
   - Should show Laravel route (not 404)

2. **Check IP Address**
   - Your current IP: `***************`
   - Make sure both devices are on same network
   - Try pinging from mobile device

#### B. Firewall/Network Issues
1. **Windows Firewall**
   - Allow Laravel dev server through firewall
   - Port 8000 should be open

2. **Router Settings**
   - Check if device isolation is enabled
   - Disable AP isolation if present

### Phase 5: Advanced Debugging 🔧

#### A. Test with Manual FCM Call
1. **Use Postman/curl to test FCM directly:**
   ```bash
   curl -X POST https://fcm.googleapis.com/v1/projects/last-5acaf/messages:send \
   -H "Authorization: Bearer [ACCESS_TOKEN]" \
   -H "Content-Type: application/json" \
   -d '{
     "message": {
       "token": "[YOUR_DEVICE_FCM_TOKEN]",
       "notification": {
         "title": "Test",
         "body": "Direct FCM test"
       }
     }
   }'
   ```

#### B. Check FCM Token Validity
1. **Token Format Check**
   - Should be ~150+ characters
   - Starts with letters/numbers
   - No spaces or special characters

2. **Token Refresh**
   - Use the FCM Refresh component in app
   - Check if new token works

### Phase 6: Common Solutions 🛠️

#### Solution A: Clear App Data
1. **Android Settings > Apps > Alerto**
   - Storage > Clear Data
   - Reinstall app
   - Re-login and check FCM registration

#### Solution B: Update Google Play Services
1. **Play Store > Google Play Services**
   - Update to latest version
   - Restart device

#### Solution C: Check Background App Restrictions
1. **Settings > Battery > Background App Refresh**
   - Enable for Alerto
   - Disable battery optimization for Alerto

#### Solution D: Network Reset
1. **Change to different network**
   - Try mobile hotspot
   - Test if notifications work on different network

### Phase 7: Logs to Check 📋

#### Mobile App Logs (Chrome DevTools)
```
🎫 Attempting to get FCM token...
✅ FCM Token retrieved successfully: [token]
Token registered with backend: [response]
Setting up Capacitor Firebase Messaging notification listeners
```

#### Backend Logs (Laravel)
```
Token registered with backend
FCM notification sent successfully
```

#### Android System Logs (adb logcat)
```bash
adb logcat | grep -i firebase
adb logcat | grep -i fcm
adb logcat | grep -i notification
```

## 🎯 Most Likely Issues

1. **Device notification permissions not granted**
2. **Google Play Services outdated/disabled**
3. **Network connectivity issues (IP/firewall)**
4. **FCM token not properly registered with backend**
5. **Background app restrictions enabled**

## 🚀 Quick Test Commands

```bash
# Build and test
cd mobile_ionic
ionic capacitor build android
# Copy google-services.json
copy google-services.json android\app\
copy google-services.json android\capacitor-cordova-android-plugins\
# Open in Android Studio
npx cap open android
```

## 📞 Next Steps

1. Follow Phase 2 first (device settings)
2. Check Chrome DevTools for FCM logs
3. Verify backend connectivity
4. Test with different network if needed
5. Report back with specific error messages found
