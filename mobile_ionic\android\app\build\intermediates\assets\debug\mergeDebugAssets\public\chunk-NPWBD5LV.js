import{a as ot}from"./chunk-4PHGPBSW.js";import"./chunk-UFD7UJFV.js";import{a as it}from"./chunk-4ZIMZLXT.js";import{a as g}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as T,$a as K,A as k,C as b,Cb as et,D as d,Da as R,Db as nt,F as n,G as i,H as C,I as v,J as y,Ja as N,K as m,M as o,N as _,Na as $,Oa as V,Pa as B,Q as P,Qa as U,R as S,Ra as j,S as I,Sa as z,Ta as W,V as O,W as D,Wa as H,X as E,ab as q,bb as G,cb as J,fb as Y,g as h,ha as L,ka as A,oa as F,q as x,qb as Q,r as w,ub as X,vb as Z,y as a,z as p,zb as tt}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as l}from"./chunk-2R6CW7ES.js";function st(r,f){r&1&&(n(0,"div",10),C(1,"ion-spinner"),n(2,"p"),o(3,"Running diagnostics..."),i()())}function rt(r,f){if(r&1){let t=v();n(0,"ion-item",12),y("click",function(){let s=x(t).$implicit,c=m(2);return w(c.showDetails(s))}),C(1,"ion-icon",13),n(2,"ion-label")(3,"h3"),o(4),i(),n(5,"p"),o(6),i()(),C(7,"ion-icon",14),i()}if(r&2){let t=f.$implicit,e=m(2);a(),d("name",e.getStatusIcon(t.status))("color",e.getStatusColor(t.status)),a(3),_(t.test),a(2),_(t.message)}}function at(r,f){if(r&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),o(3,"Diagnostic Results"),i(),n(4,"ion-card-subtitle"),o(5,"Mobile login troubleshooting"),i()(),n(6,"ion-card-content"),b(7,rt,8,4,"ion-item",11),i()()),r&2){let t=m();a(7),d("ngForOf",t.diagnostics)}}function ct(r,f){if(r&1){let t=v();n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),o(3,"Test Credentials"),i(),n(4,"ion-card-subtitle"),o(5,"Credentials used for API testing"),i()(),n(6,"ion-card-content")(7,"ion-item")(8,"ion-label",15),o(9,"Test Email"),i(),n(10,"ion-input",16),I("ngModelChange",function(s){x(t);let c=m();return S(c.testCredentials.email,s)||(c.testCredentials.email=s),w(s)}),i()(),n(11,"ion-item")(12,"ion-label",15),o(13,"Test Password"),i(),n(14,"ion-input",17),I("ngModelChange",function(s){x(t);let c=m();return S(c.testCredentials.password,s)||(c.testCredentials.password=s),w(s)}),i()(),n(15,"p",18),C(16,"ion-icon",19),o(17," These credentials are used only for testing API connectivity. A 401 error is expected and indicates the API is working correctly. "),i()()()}if(r&2){let t=m();a(10),P("ngModel",t.testCredentials.email),a(4),P("ngModel",t.testCredentials.password)}}function lt(r,f){if(r&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),o(3,"Environment Configuration"),i(),n(4,"ion-card-subtitle"),o(5,"Current app configuration"),i()(),n(6,"ion-card-content")(7,"ion-item")(8,"ion-label")(9,"h3"),o(10,"API URL"),i(),n(11,"p"),o(12),i()()(),n(13,"ion-item")(14,"ion-label")(15,"h3"),o(16,"Production Mode"),i(),n(17,"p"),o(18),i()()(),n(19,"ion-item")(20,"ion-label")(21,"h3"),o(22,"Platform"),i(),n(23,"p"),o(24),i()()()()()),r&2){let t=m();a(12),_(t.getApiUrl()),a(6),_(t.isProduction()?"Yes":"No"),a(6),_(t.getPlatformInfo())}}function dt(r,f){r&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),o(3,"Common Mobile Login Issues"),i(),n(4,"ion-card-subtitle"),o(5,"Troubleshooting guide"),i()(),n(6,"ion-card-content")(7,"div",20)(8,"h4"),o(9,"\u{1F310} Network Connectivity"),i(),n(10,"p"),o(11,"Ensure your mobile device and computer are on the same WiFi network."),i()(),n(12,"div",20)(13,"h4"),o(14,"\u{1F525} Firewall/Security"),i(),n(15,"p"),o(16,"Check if your computer's firewall is blocking port 8000."),i()(),n(17,"div",20)(18,"h4"),o(19,"\u{1F5A5}\uFE0F Backend Server"),i(),n(20,"p"),o(21,"Verify the Laravel backend is running on your computer."),i()(),n(22,"div",20)(23,"h4"),o(24,"\u{1F4F1} CORS Configuration"),i(),n(25,"p"),o(26,"Ensure CORS allows requests from mobile devices."),i()(),n(27,"div",20)(28,"h4"),o(29,"\u{1F527} IP Address"),i(),n(30,"p"),o(31,"Verify the IP address in environment.ts matches your computer's IP."),i()()()())}var yt=(()=>{class r{constructor(t,e,s,c,M,u){this.http=t,this.platform=e,this.alertCtrl=s,this.toastCtrl=c,this.authService=M,this.fcmService=u,this.diagnostics=[],this.isRunning=!1,this.testCredentials={email:"<EMAIL>",password:"password123"}}ngOnInit(){this.runDiagnostics()}runDiagnostics(){return l(this,null,function*(){this.isRunning=!0,this.diagnostics=[],yield this.testPlatform(),yield this.testNetworkConnectivity(),yield this.testBackendConnectivity(),yield this.testApiEndpoints(),yield this.testCorsConfiguration(),yield this.testFcmToken(),yield this.testLocalStorage(),yield this.testLoginApi(),this.isRunning=!1,yield this.showDiagnosticSummary()})}testPlatform(){return l(this,null,function*(){let t={test:"Platform Detection",status:"pending",message:"Detecting platform..."};this.diagnostics.push(t);try{let e={isMobile:this.platform.is("mobile"),isAndroid:this.platform.is("android"),isIOS:this.platform.is("ios"),isCordova:this.platform.is("cordova"),isCapacitor:this.platform.is("capacitor"),isBrowser:!this.platform.is("cordova")&&!this.platform.is("capacitor"),userAgent:navigator.userAgent};t.status="success",t.message=`Platform: ${this.platform.is("android")?"Android":this.platform.is("ios")?"iOS":"Browser"}`,t.details=e}catch(e){t.status="error",t.message=`Platform detection failed: ${e}`}})}testNetworkConnectivity(){return l(this,null,function*(){let t={test:"Network Connectivity",status:"pending",message:"Testing network connectivity..."};this.diagnostics.push(t);try{let e=navigator.onLine;if(e){let s=yield h(this.http.get("https://httpbin.org/get"));t.status="success",t.message="Network connectivity: Online",t.details={online:e,externalTest:"success"}}else t.status="error",t.message="Network connectivity: Offline",t.details={online:e}}catch(e){t.status="warning",t.message=`Network test failed: ${e}`,t.details={online:navigator.onLine,error:e}}})}testBackendConnectivity(){return l(this,null,function*(){let t={test:"Backend Connectivity",status:"pending",message:"Testing backend server connectivity..."};this.diagnostics.push(t);try{console.log("Testing backend at:",g.apiUrl);let e=g.apiUrl.replace("/api","/up"),s=yield h(this.http.get(e));t.status="success",t.message=`Backend server accessible at ${g.apiUrl}`,t.details={url:e,response:s}}catch(e){t.status="error",t.message=`Backend server not accessible: ${e.status||"Network error"}`,t.details={url:g.apiUrl,error:e.message||e,status:e.status,statusText:e.statusText}}})}testApiEndpoints(){return l(this,null,function*(){let t={test:"API Endpoints",status:"pending",message:"Testing API endpoints..."};this.diagnostics.push(t);try{let e=yield h(this.http.get(`${g.apiUrl}/evacuation-centers`));t.status="success",t.message="API endpoints accessible",t.details={evacuationCenters:Array.isArray(e)?e.length:"Invalid response"}}catch(e){t.status="error",t.message=`API endpoints not accessible: ${e.status||"Network error"}`,t.details={error:e.message||e}}})}testCorsConfiguration(){return l(this,null,function*(){let t={test:"CORS Configuration",status:"pending",message:"Testing CORS configuration..."};this.diagnostics.push(t);try{let e=yield h(this.http.options(`${g.apiUrl}/test`,{headers:{"Content-Type":"application/json"}}));t.status="success",t.message="CORS configuration working",t.details={response:e}}catch(e){e.status===0?(t.status="error",t.message="CORS error: Request blocked by browser"):(t.status="warning",t.message=`CORS test inconclusive: ${e.status}`),t.details={error:e}}})}testFcmToken(){return l(this,null,function*(){let t={test:"FCM Token Generation",status:"pending",message:"Testing FCM token generation..."};this.diagnostics.push(t);try{let e=yield this.fcmService.getToken();e?(t.status="success",t.message="FCM token generated successfully",t.details={tokenLength:e.length,tokenPreview:e.substring(0,20)+"..."}):(t.status="warning",t.message="FCM token generation returned empty",t.details={token:e})}catch(e){t.status="error",t.message=`FCM token generation failed: ${e}`,t.details={error:e}}})}testLocalStorage(){return l(this,null,function*(){let t={test:"LocalStorage Functionality",status:"pending",message:"Testing localStorage functionality..."};this.diagnostics.push(t);try{let e="diagnostic_test",s="test_value_"+Date.now();localStorage.setItem(e,s);let c=localStorage.getItem(e);localStorage.removeItem(e),c===s?(t.status="success",t.message="LocalStorage working correctly",t.details={test:"passed"}):(t.status="error",t.message="LocalStorage read/write failed",t.details={expected:s,actual:c})}catch(e){t.status="error",t.message=`LocalStorage error: ${e}`,t.details={error:e}}})}testLoginApi(){return l(this,null,function*(){let t={test:"Login API Call",status:"pending",message:"Testing login API call..."};this.diagnostics.push(t);try{let e=yield h(this.http.post(`${g.apiUrl}/auth/login`,this.testCredentials));t.status="success",t.message="Login API call successful (unexpected!)",t.details={response:e}}catch(e){e.status===401?(t.status="success",t.message="Login API reachable (401 expected for test credentials)",t.details={status:e.status,message:"API working correctly"}):e.status===0?(t.status="error",t.message="Login API not reachable (network error)",t.details={error:"Network connectivity issue"}):(t.status="warning",t.message=`Login API returned unexpected status: ${e.status}`,t.details={status:e.status,error:e.message})}})}showDiagnosticSummary(){return l(this,null,function*(){let t=this.diagnostics.filter(u=>u.status==="success").length,e=this.diagnostics.filter(u=>u.status==="error").length,s=this.diagnostics.filter(u=>u.status==="warning").length,c=`Diagnostics Complete:
\u2705 ${t} passed
\u26A0\uFE0F ${s} warnings
\u274C ${e} failed`;e>0&&(c+=`

Check the failed tests to identify login issues.`),yield(yield this.alertCtrl.create({header:"Diagnostic Results",message:c,buttons:["OK"]})).present()})}getStatusIcon(t){switch(t){case"success":return"checkmark-circle";case"error":return"close-circle";case"warning":return"warning";case"pending":return"time";default:return"help-circle"}}getStatusColor(t){switch(t){case"success":return"success";case"error":return"danger";case"warning":return"warning";case"pending":return"medium";default:return"medium"}}showDetails(t){return l(this,null,function*(){yield(yield this.alertCtrl.create({header:t.test,message:`Status: ${t.message}

Details: ${JSON.stringify(t.details,null,2)}`,buttons:["OK"]})).present()})}getApiUrl(){return g.apiUrl}isProduction(){return g.production}getPlatformInfo(){return this.platform.is("android")?"Android":this.platform.is("ios")?"iOS":this.platform.is("capacitor")?"Capacitor":this.platform.is("cordova")?"Cordova":"Browser"}static{this.\u0275fac=function(e){return new(e||r)(p(T),p(R),p(tt),p(et),p(ot),p(it))}}static{this.\u0275cmp=k({type:r,selectors:[["app-login-debug"]],decls:18,vars:8,consts:[[3,"translucent"],["slot","end"],[3,"click","disabled"],["name","refresh"],[3,"fullscreen"],["collapse","condense"],["size","large"],[1,"diagnostic-container"],["class","loading-container",4,"ngIf"],[4,"ngIf"],[1,"loading-container"],["button","",3,"click",4,"ngFor","ngForOf"],["button","",3,"click"],["slot","start",3,"name","color"],["name","chevron-forward","slot","end"],["position","floating"],["type","email",3,"ngModelChange","ngModel"],["type","password",3,"ngModelChange","ngModel"],[1,"note"],["name","information-circle","color","primary"],[1,"issue-item"]],template:function(e,s){e&1&&(n(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),o(3,"Login Diagnostics"),i(),n(4,"ion-buttons",1)(5,"ion-button",2),y("click",function(){return s.runDiagnostics()}),C(6,"ion-icon",3),i()()()(),n(7,"ion-content",4)(8,"ion-header",5)(9,"ion-toolbar")(10,"ion-title",6),o(11,"Login Debug"),i()()(),n(12,"div",7),b(13,st,4,0,"div",8)(14,at,8,1,"ion-card",9)(15,ct,18,2,"ion-card",9)(16,lt,25,3,"ion-card",9)(17,dt,32,0,"ion-card",9),i()()),e&2&&(d("translucent",!0),a(5),d("disabled",s.isRunning),a(2),d("fullscreen",!0),a(6),d("ngIf",s.isRunning),a(),d("ngIf",!s.isRunning&&s.diagnostics.length>0),a(),d("ngIf",!s.isRunning),a(),d("ngIf",!s.isRunning),a(),d("ngIf",!s.isRunning))},dependencies:[nt,$,V,B,U,j,z,W,H,K,q,G,J,Y,Q,X,Z,N,E,O,D,F,L,A],styles:[".debug-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;padding:16px}.diagnostic-item[_ngcontent-%COMP%]{margin-bottom:16px;border-radius:8px;overflow:hidden}.diagnostic-header[_ngcontent-%COMP%]{padding:12px 16px;font-weight:600;color:#fff;display:flex;align-items:center;gap:8px}.diagnostic-header.pending[_ngcontent-%COMP%]{background-color:var(--ion-color-warning)}.diagnostic-header.success[_ngcontent-%COMP%]{background-color:var(--ion-color-success)}.diagnostic-header.error[_ngcontent-%COMP%]{background-color:var(--ion-color-danger)}.diagnostic-header.warning[_ngcontent-%COMP%]{background-color:var(--ion-color-warning)}.diagnostic-content[_ngcontent-%COMP%]{padding:16px;background-color:var(--ion-color-light);border-left:4px solid var(--ion-color-medium)}.diagnostic-content.pending[_ngcontent-%COMP%]{border-left-color:var(--ion-color-warning)}.diagnostic-content.success[_ngcontent-%COMP%]{border-left-color:var(--ion-color-success)}.diagnostic-content.error[_ngcontent-%COMP%]{border-left-color:var(--ion-color-danger)}.diagnostic-content.warning[_ngcontent-%COMP%]{border-left-color:var(--ion-color-warning)}.diagnostic-message[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:8px}.diagnostic-details[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium);font-family:monospace;background-color:var(--ion-color-step-50);padding:8px;border-radius:4px;white-space:pre-wrap;word-break:break-all}.test-credentials[_ngcontent-%COMP%]{background-color:var(--ion-color-step-100);padding:12px;border-radius:8px;margin-bottom:16px}.test-credentials[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-primary)}.test-credentials[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:.9rem;color:var(--ion-color-medium)}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px;margin-top:16px}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{flex:1}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-card-title[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.status-icon[_ngcontent-%COMP%]{font-size:1.2rem}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:20px}.error-text[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.success-text[_ngcontent-%COMP%]{color:var(--ion-color-success)}.warning-text[_ngcontent-%COMP%]{color:var(--ion-color-warning)}",".diagnostic-container[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;text-align:center}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0}.note[_ngcontent-%COMP%]{margin-top:16px;padding:12px;background:#f0f8ff;border-radius:8px;font-size:14px;color:#666;display:flex;align-items:flex-start;gap:8px}.issue-item[_ngcontent-%COMP%]{margin-bottom:16px;padding-bottom:16px;border-bottom:1px solid #eee}.issue-item[_ngcontent-%COMP%]:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}.issue-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;font-size:16px;font-weight:600}.issue-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:#666;line-height:1.4}"]})}}return r})();export{yt as LoginDebugPage};
