<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>🚨 FCM Debug Panel</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">FCM Debug</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="container">
    <!-- Device Info Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>📱 Device Info</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label>
            <h3>Platform</h3>
            <p>{{ platformInfo }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h3>Is Capacitor</h3>
            <p>{{ isCapacitor ? 'Yes' : 'No' }}</p>
          </ion-label>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- FCM Token Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>🎫 FCM Token</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label>
            <h3>Status</h3>
            <p>{{ tokenStatus }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h3>Token Preview</h3>
            <p>{{ tokenPreview }}</p>
          </ion-label>
          <ion-button fill="clear" (click)="copyTokenToClipboard()" [disabled]="!fcmToken">
            <ion-icon name="copy"></ion-icon>
          </ion-button>
        </ion-item>
        <ion-button expand="block" fill="outline" (click)="refreshToken()">
          🔄 Refresh FCM Token
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Backend Connection Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>🌐 Backend Connection</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label>
            <h3>API URL</h3>
            <p>{{ apiUrl }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h3>Connection Status</h3>
            <p>{{ connectionStatus }}</p>
          </ion-label>
        </ion-item>
        <ion-button expand="block" fill="outline" (click)="testBackendConnection()">
          🔗 Test Backend Connection
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Notification Test Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>📬 Notification Test</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-button expand="block" fill="outline" (click)="testLocalNotification()">
          🔔 Test Local Notification
        </ion-button>
        <ion-button expand="block" (click)="sendTestNotification()" [disabled]="!fcmToken">
          📤 Send Test FCM Notification
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Debug Logs Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>📋 Debug Logs</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="log-container">
          <div *ngFor="let log of debugLogs" [class]="'log-' + log.type">
            <small>{{ log.timestamp }}</small> - {{ log.message }}
          </div>
          <div *ngIf="debugLogs.length === 0" class="no-logs">
            No logs yet...
          </div>
        </div>
        <ion-button expand="block" fill="clear" (click)="clearLogs()">
          🗑️ Clear Logs
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Instructions -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>📖 Instructions</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ol>
          <li>Check if FCM token is retrieved successfully</li>
          <li>Test backend connection</li>
          <li>Try local notification first</li>
          <li>Send test FCM notification</li>
          <li>Check device notification settings if tests fail</li>
        </ol>
        
        <ion-note>
          <p><strong>Common Issues:</strong></p>
          <ul>
            <li>Notifications disabled in device settings</li>
            <li>Google Play Services outdated</li>
            <li>Network connectivity issues</li>
            <li>Battery optimization enabled for app</li>
          </ul>
        </ion-note>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
