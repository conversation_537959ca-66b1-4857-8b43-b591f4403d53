@echo off
echo ========================================
echo 🚨 FCM Notification Setup Test
echo ========================================
echo.

echo 📱 Step 1: Building app with FCM configuration...
echo.

REM Build the app
echo Building Ionic app for Android...
call ionic capacitor build android --no-open
if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed! Check for errors above.
    pause
    exit /b 1
)

echo.
echo 📋 Step 2: Copying FCM configuration files...

REM Copy google-services.json to correct locations
echo Copying google-services.json to Android project...
if exist google-services.json (
    copy /Y google-services.json android\app\
    copy /Y google-services.json android\capacitor-cordova-android-plugins\
    echo ✅ google-services.json copied successfully
) else (
    echo ❌ google-services.json not found in root directory!
    echo Please make sure google-services.json exists in mobile_ionic folder
    pause
    exit /b 1
)

echo.
echo 🔧 Step 3: Checking Android configuration...

REM Check if AndroidManifest.xml has FCM service
findstr /C:"FirebaseMessagingService" android\app\src\main\AndroidManifest.xml >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ FCM service found in AndroidManifest.xml
) else (
    echo ❌ FCM service not found in AndroidManifest.xml
)

REM Check if POST_NOTIFICATIONS permission exists
findstr /C:"POST_NOTIFICATIONS" android\app\src\main\AndroidManifest.xml >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ POST_NOTIFICATIONS permission found
) else (
    echo ❌ POST_NOTIFICATIONS permission missing
)

echo.
echo 🌐 Step 4: Checking network configuration...
echo Current API URL in environment.ts:
findstr /C:"apiUrl" src\environments\environment.ts

echo.
echo 📱 Step 5: Opening Android Studio...
echo.
echo ⚠️  IMPORTANT: Before testing, make sure to:
echo    1. Enable notifications in device settings for Alerto
echo    2. Disable battery optimization for Alerto
echo    3. Make sure Google Play Services is updated
echo    4. Check that your device is on the same network as backend
echo.
echo 🔍 After installing the app:
echo    1. Connect device via USB
echo    2. Open Chrome and go to chrome://inspect
echo    3. Select your device and app
echo    4. Check console for FCM logs:
echo       - "FCM Token retrieved successfully"
echo       - "Token registered with backend"
echo.

REM Open Android Studio
call npx cap open android

echo.
echo 📋 Debugging checklist:
echo    □ App installs without crashing
echo    □ FCM token appears in Chrome DevTools console
echo    □ Token registration succeeds (check console)
echo    □ Device notifications are enabled in settings
echo    □ Backend receives the FCM token (check Laravel logs)
echo    □ Test notification from admin panel
echo.
echo 🚨 If notifications still don't work, check:
echo    1. Device notification settings (Settings > Apps > Alerto)
echo    2. Google Play Services version
echo    3. Network connectivity (try different network)
echo    4. Backend FCM configuration
echo.
pause
