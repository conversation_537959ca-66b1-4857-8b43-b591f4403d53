import { Component, OnInit } from '@angular/core';
import { Platform, AlertController, LoadingController } from '@ionic/angular';
import { FCMService } from '../../services/fcm.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-fcm-debug',
  templateUrl: './fcm-debug.page.html',
  styleUrls: ['./fcm-debug.page.scss'],
})
export class FCMDebugPage implements OnInit {
  platformInfo = '';
  isCapacitor = false;
  tokenStatus = 'Not checked';
  tokenPreview = 'None';
  fcmToken = '';
  apiUrl = environment.apiUrl;
  connectionStatus = 'Not tested';
  debugLogs: Array<{type: string, message: string, timestamp: string}> = [];

  constructor(
    private platform: Platform,
    private fcmService: FCMService,
    private alertController: <PERSON><PERSON><PERSON>ontroller,
    private loadingController: LoadingController,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.initializeDebugInfo();
  }

  private initializeDebugInfo() {
    // Platform info
    this.platformInfo = this.platform.platforms().join(', ');
    this.isCapacitor = this.platform.is('capacitor');
    
    this.addLog('info', 'FCM Debug panel initialized');
    this.addLog('info', `Platform: ${this.platformInfo}`);
    this.addLog('info', `Is Capacitor: ${this.isCapacitor}`);
    
    // Check FCM token
    this.checkFCMToken();
  }

  private addLog(type: string, message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.debugLogs.unshift({ type, message, timestamp });
    
    // Keep only last 50 logs
    if (this.debugLogs.length > 50) {
      this.debugLogs = this.debugLogs.slice(0, 50);
    }
  }

  async checkFCMToken() {
    try {
      this.addLog('info', 'Checking FCM token...');
      
      if (!this.isCapacitor) {
        this.tokenStatus = 'Browser mode - FCM not available';
        this.addLog('warning', 'Running in browser - FCM not available');
        return;
      }

      const token = await this.fcmService.getToken();
      
      if (token) {
        this.fcmToken = token;
        this.tokenPreview = token.substring(0, 20) + '...';
        this.tokenStatus = 'Token retrieved successfully';
        this.addLog('success', `FCM token retrieved: ${this.tokenPreview}`);
      } else {
        this.tokenStatus = 'Failed to get token';
        this.addLog('error', 'Failed to retrieve FCM token');
      }
    } catch (error) {
      this.tokenStatus = 'Error getting token';
      this.addLog('error', `FCM token error: ${error}`);
    }
  }

  async refreshToken() {
    const loading = await this.loadingController.create({
      message: 'Refreshing FCM token...'
    });
    await loading.present();

    try {
      this.addLog('info', 'Refreshing FCM token...');
      await this.fcmService.initPush();
      await this.checkFCMToken();
      this.addLog('success', 'FCM token refreshed');
    } catch (error) {
      this.addLog('error', `Token refresh failed: ${error}`);
    }

    await loading.dismiss();
  }

  async testBackendConnection() {
    const loading = await this.loadingController.create({
      message: 'Testing backend connection...'
    });
    await loading.present();

    try {
      this.addLog('info', `Testing connection to: ${this.apiUrl}`);
      
      // Test with a simple endpoint that should exist
      const response = await this.http.get(`${this.apiUrl.replace('/api', '')}/`, {
        timeout: 5000
      }).toPromise();
      
      this.connectionStatus = 'Connected successfully';
      this.addLog('success', 'Backend connection successful');
    } catch (error) {
      this.connectionStatus = `Connection failed: ${error}`;
      this.addLog('error', `Backend connection failed: ${error}`);
    }

    await loading.dismiss();
  }

  async testLocalNotification() {
    try {
      this.addLog('info', 'Testing local notification...');
      
      // Simple alert as local notification test
      const alert = await this.alertController.create({
        header: '🔔 Test Notification',
        message: 'This is a test local notification. If you see this, local notifications work!',
        buttons: ['OK']
      });
      
      await alert.present();
      this.addLog('success', 'Local notification test completed');
    } catch (error) {
      this.addLog('error', `Local notification test failed: ${error}`);
    }
  }

  async sendTestNotification() {
    if (!this.fcmToken) {
      this.addLog('error', 'No FCM token available for test');
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Sending test notification...'
    });
    await loading.present();

    try {
      this.addLog('info', 'Sending test FCM notification...');
      
      const payload = {
        fcm_token: this.fcmToken,
        title: '🚨 Test Emergency Alert',
        body: 'This is a test notification from FCM Debug Panel',
        category: 'earthquake'
      };

      const response = await this.http.post(`${this.apiUrl}/send-test-notification`, payload).toPromise();
      
      this.addLog('success', 'Test notification sent successfully');
    } catch (error) {
      this.addLog('error', `Test notification failed: ${error}`);
    }

    await loading.dismiss();
  }

  clearLogs() {
    this.debugLogs = [];
    this.addLog('info', 'Debug logs cleared');
  }

  async copyTokenToClipboard() {
    if (this.fcmToken) {
      try {
        await navigator.clipboard.writeText(this.fcmToken);
        this.addLog('success', 'FCM token copied to clipboard');
      } catch (error) {
        // Fallback for older browsers
        const alert = await this.alertController.create({
          header: 'FCM Token',
          message: this.fcmToken,
          buttons: ['OK']
        });
        await alert.present();
        this.addLog('info', 'FCM token displayed in alert');
      }
    }
  }
}
