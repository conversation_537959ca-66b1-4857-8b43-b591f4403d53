import{a as l1}from"./chunk-ETRRG7RM.js";import{a as n1}from"./chunk-4ZIMZLXT.js";import{a as i1}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{A as N,B as f,Da as K,Db as t1,Ea as _,F as W,G,H as $,La as o1,Y as X,Z as Q,a as P,aa as C,ba as J,d as F,da as Y,ea as k,f as I,fa as Z,ga as A,i as D,k as R,l as U,m as w,n as m,o as u,p as M,t as T,v as E,wb as a1,z,zb as s1}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as q}from"./chunk-2R6CW7ES.js";function e1(){if(typeof process>"u"){var o=typeof window<"u"?window:{},s=5e3,a=Date.now(),t=!1;o.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-a)+" ms"),t=!0}),setTimeout(function(){!t&&o.cordova&&console.warn("Ionic Native: deviceready did not fire within "+s+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},s)}}var i2={error:"cordova_not_available"},n2={error:"plugin_not_installed"};function y(o){var s=function(){if(Promise)return new Promise(function(n,l){o(n,l)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var a=window.document,t=window.angular.element(a.querySelector("[ng-app]")||a.body).injector();if(t){var i=t.get("$q");return i(function(n,l){o(n,l)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return s()}function l2(o,s,a,t){t===void 0&&(t={});var i,n,l=y(function(c,p){t.destruct?i=g(o,s,a,t,function(){for(var d=[],v=0;v<arguments.length;v++)d[v]=arguments[v];return c(d)},function(){for(var d=[],v=0;v<arguments.length;v++)d[v]=arguments[v];return p(d)}):i=g(o,s,a,t,c,p),n=p});return i&&i.error&&(l.catch(function(){}),typeof n=="function"&&n(i.error)),l}function e2(o,s,a,t){return t===void 0&&(t={}),y(function(i,n){var l=g(o,s,a,t);l?l.error?n(l.error):l.then&&l.then(i).catch(n):n({error:"unexpected_error"})})}function c2(o,s,a,t){return t===void 0&&(t={}),new P(function(i){var n;return t.destruct?n=g(o,s,a,t,function(){for(var l=[],c=0;c<arguments.length;c++)l[c]=arguments[c];return i.next(l)},function(){for(var l=[],c=0;c<arguments.length;c++)l[c]=arguments[c];return i.error(l)}):n=g(o,s,a,t,i.next.bind(i),i.error.bind(i)),n&&n.error&&(i.error(n.error),i.complete()),function(){try{if(t.clearFunction)return t.clearWithArgs?g(o,t.clearFunction,a,t,i.next.bind(i),i.error.bind(i)):g(o,t.clearFunction,[])}catch(l){console.warn("Unable to clear the previous observable watch for",o.constructor.getPluginName(),s),console.warn(l)}}})}function r2(o,s){return s=typeof window<"u"&&s?c1(window,s):s||(typeof window<"u"?window:{}),D(s,o)}function h(o,s,a){var t,i,n;return typeof o=="string"?t=o:(t=o.constructor.getPluginRef(),a=o.constructor.getPluginName(),n=o.constructor.getPluginInstallName()),i=B(t),!i||s&&typeof i[s]>"u"?typeof window>"u"||!window.cordova?(w2(a,s),i2):(g2(a,n,s),n2):!0}function v2(o,s,a,t){if(s===void 0&&(s={}),s.sync)return o;if(s.callbackOrder==="reverse")o.unshift(t),o.unshift(a);else if(s.callbackStyle==="node")o.push(function(c,p){c?t(c):a(p)});else if(s.callbackStyle==="object"&&s.successName&&s.errorName){var i={};i[s.successName]=a,i[s.errorName]=t,o.push(i)}else if(typeof s.successIndex<"u"||typeof s.errorIndex<"u"){var n=function(){s.successIndex>o.length?o[s.successIndex]=a:o.splice(s.successIndex,0,a)},l=function(){s.errorIndex>o.length?o[s.errorIndex]=t:o.splice(s.errorIndex,0,t)};s.successIndex>s.errorIndex?(l(),n()):(n(),l())}else o.push(a),o.push(t);return o}function g(o,s,a,t,i,n){t===void 0&&(t={}),a=v2(a,t,i,n);var l=h(o,s);if(l===!0){var c=B(o.constructor.getPluginRef());return c[s].apply(c,a)}else return l}function B(o){return typeof window<"u"?c1(window,o):null}function c1(o,s){for(var a=s.split("."),t=o,i=0;i<a.length;i++){if(!t)return null;t=t[a[i]]}return t}function g2(o,s,a){console.warn(a?"Native: tried calling "+o+"."+a+", but the "+o+" plugin is not installed.":"Native: tried accessing the "+o+" plugin but it's not installed."),s&&console.warn("Install the "+o+" plugin: 'ionic cordova plugin add "+s+"'")}function w2(o,s){typeof process>"u"&&console.warn(s?"Native: tried calling "+o+"."+s+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+o+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var S=function(o,s,a){return a===void 0&&(a={}),function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return a.sync?g(o,s,t,a):a.observable?c2(o,s,t,a):a.eventObservable&&a.event?r2(a.event,a.element):a.otherPromise?e2(o,s,t,a):l2(o,s,t,a)}};function r1(o,s){for(var a=s.split("."),t=o,i=0;i<a.length;i++){if(!t)return null;t=t[a[i]]}return t}var j=function(){function o(){}return o.installed=function(){var s=h(this.pluginRef)===!0;return s},o.getPlugin=function(){return typeof window<"u"?r1(window,this.pluginRef):null},o.getPluginName=function(){var s=this.pluginName;return s},o.getPluginRef=function(){var s=this.pluginRef;return s},o.getPluginInstallName=function(){var s=this.plugin;return s},o.getSupportedPlatforms=function(){var s=this.platforms;return s},o.pluginName="",o.pluginRef="",o.plugin="",o.repo="",o.platforms=[],o.install="",o}();function e(o,s,a,t){return S(o,s,a).apply(this,t)}e1();var L=function(o){F(s,o);function s(){return o!==null&&o.apply(this,arguments)||this}return s.prototype.enable=function(){return e(this,"enable",{sync:!0},arguments)},s.prototype.disable=function(){return e(this,"disable",{sync:!0},arguments)},s.prototype.setEnabled=function(a){return e(this,"setEnabled",{sync:!0},arguments)},s.prototype.fireEvent=function(a){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];return e(this,"fireEvent",{sync:!0},arguments)},s.prototype.isEnabled=function(){return e(this,"isEnabled",{sync:!0},arguments)},s.prototype.isActive=function(){return e(this,"isActive",{sync:!0},arguments)},s.prototype.setDefaults=function(a){return e(this,"setDefaults",{platforms:["Android"]},arguments)},s.prototype.configure=function(a){return e(this,"configure",{platforms:["Android"],sync:!0},arguments)},s.prototype.on=function(a){return e(this,"on",{observable:!0,clearFunction:"un",clearWithArgs:!0},arguments)},s.prototype.un=function(a,t){return e(this,"un",{},arguments)},s.prototype.moveToBackground=function(){return e(this,"moveToBackground",{platforms:["Android"],sync:!0},arguments)},s.prototype.disableWebViewOptimizations=function(){return e(this,"disableWebViewOptimizations",{platforms:["Android"],sync:!0},arguments)},s.prototype.moveToForeground=function(){return e(this,"moveToForeground",{platforms:["Android"],sync:!0},arguments)},s.prototype.overrideBackButton=function(){return e(this,"overrideBackButton",{platforms:["Android"],sync:!0},arguments)},s.prototype.excludeFromTaskList=function(){return e(this,"excludeFromTaskList",{platforms:["Android"],sync:!0},arguments)},s.prototype.isScreenOff=function(a){return e(this,"isScreenOff",{platforms:["Android"]},arguments)},s.prototype.wakeUp=function(){return e(this,"wakeUp",{platforms:["Android"],sync:!0},arguments)},s.prototype.unlock=function(){return e(this,"unlock",{platforms:["Android"],sync:!0},arguments)},s.prototype.disableBatteryOptimizations=function(){return e(this,"disableBatteryOptimizations",{platforms:["Android"],sync:!0},arguments)},s.pluginName="BackgroundMode",s.plugin="cordova-plugin-background-mode",s.pluginRef="cordova.plugins.backgroundMode",s.repo="https://github.com/katzer/cordova-plugin-background-mode",s.platforms=["AmazonFire OS","Android","Browser","iOS","Windows"],s.decorators=[{type:T}],s}(j);var O,p2=function(){if(typeof window>"u")return new Map;if(!O){var o=window;o.Ionicons=o.Ionicons||{},O=o.Ionicons.map=o.Ionicons.map||new Map}return O},b=function(o){Object.keys(o).forEach(function(s){v1(s,o[s]);var a=s.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();s!==a&&v1(a,o[s])})},v1=function(o,s){var a=p2(),t=a.get(o);t===void 0?a.set(o,s):t!==s&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(o,'". Ensure that multiple icons are not mapped to the same icon name.'))};var g1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 166.05L256 288l5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 6z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 367.91a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var w1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M268 112l144 144-144 144M392 256H100' class='ionicon-fill-none'/></svg>";var h1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M388 288a76 76 0 1076 76 76.24 76.24 0 00-76-76zM124 288a76 76 0 1076 76 76.24 76.24 0 00-76-76z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 360v-86l-64-42 80-88 40 72h56' class='ionicon-fill-none ionicon-stroke-width'/><path d='M320 136a31.89 31.89 0 0032-32.1A31.55 31.55 0 00320.2 72a32 32 0 10-.2 64z'/></svg>";var p1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M451 374c-15.88-16-54.34-39.35-73-48.76-24.3-12.24-26.3-13.24-45.4.95-12.74 9.47-21.21 17.93-36.12 14.75s-47.31-21.11-75.68-49.39-47.34-61.62-50.53-76.48 5.41-23.23 14.79-36c13.22-18 12.22-21 .92-45.3-8.81-18.9-32.84-57-48.9-72.8C119.9 44 119.9 47 108.83 51.6A160.15 160.15 0 0083 65.37C67 76 58.12 84.83 51.91 98.1s-9 44.38 23.07 102.64 54.57 88.05 101.14 134.49S258.5 406.64 310.85 436c64.76 36.27 89.6 29.2 102.91 23s22.18-15 32.83-31a159.09 159.09 0 0013.8-25.8C465 391.17 468 391.17 451 374z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var d1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 224l37.78-88.15C123.93 121.5 139.6 112 157.11 112h197.78c17.51 0 33.18 9.5 39.33 23.85L432 224M80 224h352v144H80zM112 368v32H80v-32M432 368v32h-32v-32' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='144' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='368' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var x1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>";var m1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",u1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 256c0-114.87-93.13-208-208-208S48 141.13 48 256s93.13 208 208 208 208-93.13 208-208zm-100.69-28.69l-96 96a16 16 0 01-22.62 0l-96-96a16 16 0 0122.62-22.62L256 289.37l84.69-84.68a16 16 0 0122.62 22.62z'/></svg>",M1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M352 216l-96 96-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>";var f1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 328l144-144 144 144' class='ionicon-fill-none'/></svg>";var k1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>";var B1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M368 368L144 144M368 144L144 368' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var L1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 240c-8.89-89.54-71-144-144-144-69 0-113.44 48.2-128 96-60 6-112 43.59-112 112 0 66 54 112 120 112h260c55 0 100-27.44 100-88 0-59.82-53-85.76-96-88z' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var H1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 448a15.92 15.92 0 01-11.31-4.69l-352-352a16 16 0 0122.62-22.62l352 352A16 16 0 01432 448zM255.66 384c-41.49 0-81.5-12.28-118.92-36.5-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 00.14-2.94L93.5 161.38a2 2 0 00-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0075.8-12.58 2 2 0 00.77-3.31l-21.58-21.58a4 4 0 00-3.83-1 204.8 204.8 0 01-51.16 6.47zM490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 00-74.89 12.83 2 2 0 00-.75 3.31l21.55 21.55a4 4 0 003.88 1 192.82 192.82 0 0150.21-6.69c40.69 0 80.58 12.43 118.55 37 34.71 22.4 65.74 53.88 89.76 91a.13.13 0 010 .16 310.72 310.72 0 01-64.12 72.73 2 2 0 00-.15 2.95l19.9 19.89a2 2 0 002.7.13 343.49 343.49 0 0068.64-78.48 32.2 32.2 0 00-.1-34.78z'/><path d='M256 160a95.88 95.88 0 00-21.37 2.4 2 2 0 00-1 3.38l112.59 112.56a2 2 0 003.38-1A96 96 0 00256 160zM165.78 233.66a2 2 0 00-3.38 1 96 96 0 00115 115 2 2 0 001-3.38z'/></svg>";var V1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 00-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 000-17.47C428.89 172.28 347.8 112 255.66 112z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='256' r='80' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var C1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 80a176 176 0 10176 176A176 176 0 00256 80z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 202.29s.84-17.5 19.57-32.57C230.68 160.77 244 158.18 256 158c10.93-.14 20.69 1.67 26.53 4.45 10 4.76 29.47 16.38 29.47 41.09 0 26-17 37.81-36.37 50.8S251 281.43 251 296' stroke-linecap='round' stroke-miterlimit='10' stroke-width='28' class='ionicon-fill-none'/><circle cx='250' cy='348' r='20'/></svg>";var A1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 212v236a16 16 0 0016 16h96V328a24 24 0 0124-24h80a24 24 0 0124 24v136h96a16 16 0 0016-16V212' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M480 256L266.89 52c-5-5.28-16.69-5.34-21.78 0L32 256M400 179V64h-48v69' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M220 220h32v116' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M208 340h88' class='ionicon-fill-none ionicon-stroke-width'/><path d='M248 130a26 26 0 1026 26 26 26 0 00-26-26z'/></svg>";var S1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40' class='ionicon-fill-none'/><path d='M256 112a144 144 0 10144 144 144 144 0 00-144-144z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",j1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40M256 112a144 144 0 10144 144 144 144 0 00-144-144zM416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",O1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='192' r='32'/><path d='M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z'/></svg>",b1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48c-79.5 0-144 61.39-144 137 0 87 96 224.87 131.25 272.49a15.77 15.77 0 0025.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='192' r='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M336 208v-95a80 80 0 00-160 0v95' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='208' width='320' height='272' rx='48' ry='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var P1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><rect x='48' y='96' width='416' height='320' rx='40' ry='40' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M112 160l144 112 144-112' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var F1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M313.27 124.64L198.73 51.36a32 32 0 00-29.28.35L56.51 127.49A16 16 0 0048 141.63v295.8a16 16 0 0023.49 14.14l97.82-63.79a32 32 0 0129.5-.24l111.86 73a32 32 0 0029.27-.11l115.43-75.94a16 16 0 008.63-14.2V74.57a16 16 0 00-23.49-14.14l-98 63.86a32 32 0 01-29.24.35zM328 128v336M184 48v336' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var I1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var D1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M272 464a16 16 0 01-16-16.42V264.13a8 8 0 00-8-8H64.41a16.31 16.31 0 01-15.49-10.65 16 16 0 018.41-19.87l384-176.15a16 16 0 0121.22 21.19l-176 384A16 16 0 01272 464z'/></svg>";var R1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 64L64 240.14h200a8 8 0 018 8V448z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var U1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M427.68 351.43C402 320 383.87 304 383.87 217.35 383.87 138 343.35 109.73 310 96c-4.43-1.82-8.6-6-9.95-10.55C294.2 65.54 277.8 48 256 48s-38.21 17.55-44 37.47c-1.35 4.6-5.52 8.71-9.95 10.53-33.39 13.75-73.87 41.92-73.87 121.35C128.13 304 110 320 84.32 351.43 73.68 364.45 83 384 101.61 384h308.88c18.51 0 27.77-19.61 17.19-32.57zM320 384v16a64 64 0 01-128 0v-16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var T1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M402 168c-2.93 40.67-33.1 72-66 72s-63.12-31.32-66-72c-3-42.31 26.37-72 66-72s69 30.46 66 72z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M336 304c-65.17 0-127.84 32.37-143.54 95.41-2.08 8.34 3.15 16.59 11.72 16.59h263.65c8.57 0 13.77-8.25 11.72-16.59C463.85 335.36 401.18 304 336 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 185.94c-2.34 32.48-26.72 58.06-53 58.06s-50.7-25.57-53-58.06C91.61 152.15 115.34 128 147 128s55.39 24.77 53 57.94z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M206 306c-18.05-8.27-37.93-11.45-59-11.45-52 0-102.1 25.85-114.65 76.2-1.65 6.66 2.53 13.25 9.37 13.25H154' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var E1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M344 144c-3.92 52.87-44 96-88 96s-84.15-43.12-88-96c-4-55 35-96 88-96s92 42 88 96z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 304c-87 0-175.3 48-191.64 138.6C62.39 453.52 68.57 464 80 464h352c11.44 0 17.62-10.48 15.65-21.4C431.3 352 343 304 256 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var N1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M320 146s24.36-12-64-12a160 160 0 10160 160' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 58l80 80-80 80' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var W1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M456.69 421.39L362.6 327.3a173.81 173.81 0 0034.84-104.58C397.44 126.38 319.06 48 222.72 48S48 126.38 48 222.72s78.38 174.72 174.72 174.72A173.81 173.81 0 00327.3 362.6l94.09 94.09a25 25 0 0035.3-35.3zM97.92 222.72a124.8 124.8 0 11124.8 124.8 124.95 124.95 0 01-124.8-124.8z'/></svg>";var G1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var $1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M262.29 192.31a64 64 0 1057.4 57.4 64.13 64.13 0 00-57.4-57.4zM416.39 256a154.34 154.34 0 01-1.53 20.79l45.21 35.46a10.81 10.81 0 012.45 13.75l-42.77 74a10.81 10.81 0 01-13.14 4.59l-44.9-18.08a16.11 16.11 0 00-15.17 1.75A164.48 164.48 0 01325 400.8a15.94 15.94 0 00-8.82 12.14l-6.73 47.89a11.08 11.08 0 01-10.68 9.17h-85.54a11.11 11.11 0 01-10.69-8.87l-6.72-47.82a16.07 16.07 0 00-9-12.22 155.3 155.3 0 01-21.46-12.57 16 16 0 00-15.11-1.71l-44.89 18.07a10.81 10.81 0 01-13.14-4.58l-42.77-74a10.8 10.8 0 012.45-13.75l38.21-30a16.05 16.05 0 006-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 00-6.07-13.94l-38.19-30A10.81 10.81 0 0149.48 186l42.77-74a10.81 10.81 0 0113.14-4.59l44.9 18.08a16.11 16.11 0 0015.17-1.75A164.48 164.48 0 01187 111.2a15.94 15.94 0 008.82-12.14l6.73-47.89A11.08 11.08 0 01213.23 42h85.54a11.11 11.11 0 0110.69 8.87l6.72 47.82a16.07 16.07 0 009 12.22 155.3 155.3 0 0121.46 12.57 16 16 0 0015.11 1.71l44.89-18.07a10.81 10.81 0 0113.14 4.58l42.77 74a10.8 10.8 0 01-2.45 13.75l-38.21 30a16.05 16.05 0 00-6.05 14.08c.33 4.14.55 8.3.55 12.47z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var X1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M120 352l-24 48M136 432l-16 32M400 352l-24 48M416 432l-16 32M208 304l-16 96h48v80l80-112h-48l16-64M404.33 152.89H392.2C384.71 84.85 326.14 32 256 32a136.39 136.39 0 00-128.63 90.67h-4.57c-49.94 0-90.8 40.8-90.8 90.66h0C32 263.2 72.86 304 122.8 304h281.53C446 304 480 270 480 228.44h0c0-41.55-34-75.55-75.67-75.55z' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 128v144h96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var J1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M314.21 482.32l-56.77-114.74-44.89-57.39a72.82 72.82 0 01-10.13-37.05V144h15.67a40.22 40.22 0 0140.23 40.22v183.36M127.9 293.05v-74.52S165.16 144 202.42 144M370.1 274.42L304 231M170.53 478.36L224 400' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='258.32' cy='69.48' r='37.26' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M85.57 446.25h340.86a32 32 0 0028.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0028.17 47.17z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 195.39l5.74 122 5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 5.95z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 397.25a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var Z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 320c0 88.37-55.63 144-144 144s-144-55.63-144-144c0-94.83 103.23-222.85 134.89-259.88a12 12 0 0118.23 0C296.77 97.15 400 225.17 400 320z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M344 328a72 72 0 01-72 72' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";function H(){b({"thunderstorm-outline":X1,"water-outline":Z1,"cloud-outline":L1,"chevron-up":f1,"chevron-back-outline":x1,"chevron-down":m1,"chevron-down-circle":u1,"chevron-down-circle-outline":M1,"chevron-down-outline":z1,"arrow-forward-outline":w1,"walk-outline":J1,"bicycle-outline":h1,"car-outline":d1,"navigate-outline":R1,navigate:D1,"location-outline":b1,location:O1,"locate-outline":S1,locate:j1,"map-outline":F1,"time-outline":Q1,"information-circle-outline":y1,"help-circle-outline":C1,"alert-circle-outline":g1,"warning-outline":Y1,"call-outline":p1,"people-outline":T1,"person-outline":E1,"mail-outline":P1,"close-outline":B1,"close-circle":k1,"search-outline":G1,search:W1,"refresh-outline":N1,"menu-outline":I1,"settings-outline":$1,"home-outline":A1,"notifications-outline":U1,"lock-closed-outline":q1,"eye-outline":V1,"eye-off-outline":H1})}var K1=(()=>{class o{constructor(a,t,i){this.platform=a,this.fcmService=t,this.backgroundMode=i;try{H()}catch(n){console.log("Error registering icons:",n)}this.initializeApp()}initializeApp(){this.platform.ready().then(()=>{try{console.log("App initialization started"),this.checkForNotificationInUrl(),this.initializeFCM(),console.log("App initialization completed successfully")}catch(a){console.error("Error during app initialization:",a)}}).catch(a=>{console.error("Error in platform.ready():",a)})}initializeFCM(){console.log("\u{1F525} Initializing FCM for all users (authenticated or not)");try{this.fcmService.initPush().then(()=>{console.log("\u2705 FCM initialization completed")}).catch(a=>{console.error("\u274C FCM initialization failed:",a)})}catch(a){console.error("\u274C FCM initialization error (sync):",a)}}checkForNotificationInUrl(){try{let a=new URL(window.location.href),t=a.searchParams.get("notification");if(t)try{let i=JSON.parse(decodeURIComponent(t));a.searchParams.delete("notification"),window.history.replaceState({},document.title,a.toString())}catch(i){console.error("Error parsing notification from URL:",i)}}catch(a){console.error("Error checking for notification in URL:",a)}}static{this.\u0275fac=function(t){return new(t||o)(z(K),z(n1),z(L))}}static{this.\u0275cmp=N({type:o,selectors:[["app-root"]],standalone:!1,decls:2,vars:0,template:function(t,i){t&1&&(W(0,"ion-app"),$(1,"ion-router-outlet"),G())},dependencies:[o1,a1],encapsulation:2})}}return o})();var r=(o,s)=>localStorage.getItem("token")?!0:(M(k).navigate(["/login"]),!1);var _1=(o,s)=>localStorage.getItem("onboardingComplete")==="true"?!0:(M(k).navigate(["/welcome"]),!1);var x2=[{path:"",redirectTo:"loading",pathMatch:"full"},{path:"loading",loadComponent:()=>import("./chunk-HOAALLB5.js").then(o=>o.LoadingPage)},{path:"login",loadComponent:()=>import("./chunk-CKSSHOL2.js").then(o=>o.LoginPage)},{path:"register",loadComponent:()=>import("./chunk-LVZE5EDQ.js").then(o=>o.RegisterPage)},{path:"environment-switcher",loadComponent:()=>import("./chunk-FAORU32O.js").then(o=>o.EnvironmentSwitcherPage)},{path:"network-diagnostics",loadComponent:()=>import("./chunk-NPWBD5LV.js").then(o=>o.LoginDebugPage)},{path:"notification-test",loadComponent:()=>import("./chunk-YNS6ZPLC.js").then(o=>o.NotificationTestPage)},{path:"welcome",loadComponent:()=>import("./chunk-65VDQ2WG.js").then(o=>o.WelcomePage),canActivate:[r]},{path:"data",loadComponent:()=>import("./chunk-MZQJKICX.js").then(o=>o.DataPage),canActivate:[r]},{path:"settings",loadComponent:()=>import("./chunk-KLDDBOVA.js").then(o=>o.SettingsPage),canActivate:[r]},{path:"notifications",loadComponent:()=>import("./chunk-Z3BEI2NT.js").then(o=>o.NotificationsPage),canActivate:[r]},{path:"earthquake-map",loadComponent:()=>import("./chunk-43MH5CAJ.js").then(o=>o.EarthquakeMapPage),canActivate:[r]},{path:"typhoon-map",loadComponent:()=>import("./chunk-K7QK63J2.js").then(o=>o.TyphoonMapPage),canActivate:[r]},{path:"flood-map",loadComponent:()=>import("./chunk-3XM6LHBB.js").then(o=>o.FloodMapPage),canActivate:[r]},{path:"all-maps",loadComponent:()=>import("./chunk-JXR2HMSP.js").then(o=>o.AllMapsPage),canActivate:[r]},{path:"tabs",loadComponent:()=>import("./chunk-RGLBX52B.js").then(o=>o.TabsPage),canActivate:[r,_1],children:[{path:"home",loadComponent:()=>import("./chunk-L3JBG7LS.js").then(o=>o.HomePage)},{path:"search",loadComponent:()=>import("./chunk-QMWMG6JV.js").then(o=>o.SearchPage)},{path:"map",loadComponent:()=>import("./chunk-ZTDRKEJH.js").then(o=>o.MapPage)},{path:"profile",loadComponent:()=>import("./chunk-T653MMGC.js").then(o=>o.ProfilePage)},{path:"",redirectTo:"home",pathMatch:"full"}]}],o2=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=f({type:o})}static{this.\u0275inj=m({imports:[A.forRoot(x2,{preloadingStrategy:Z}),A]})}}return o})();var V=(()=>{class o{constructor(a){this.alertCtrl=a}handleError(a){let t="An unknown error occurred";return a.error instanceof ErrorEvent?(t=`Error: ${a.error.message}`,console.error("Client-side error:",a.error.message)):(t=`Error Code: ${a.status}
Message: ${a.message}`,console.error(`Server-side error: Status: ${a.status}, Body: ${JSON.stringify(a.error)}`)),console.error("HTTP error:",a),I(()=>a)}showErrorAlert(a,t){return q(this,null,function*(){yield(yield this.alertCtrl.create({header:a,message:t,buttons:["OK"]})).present()})}handleAppError(a,t){console.error(`Error in ${t||"Unknown component"}:`,a),a&&a.isCritical&&this.showErrorAlert("Application Error","An unexpected error occurred. Please restart the application.")}static{this.\u0275fac=function(t){return new(t||o)(u(s1))}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();var a2=(()=>{class o{constructor(a){this.errorHandlerService=a}intercept(a,t){return t.handle(a).pipe(U(1),R(i=>(console.log("HTTP Error Interceptor caught an error:",i),this.errorHandlerService.handleError(i))))}static{this.\u0275fac=function(t){return new(t||o)(u(V))}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac})}}return o})();var s2=(()=>{class o{constructor(){}intercept(a,t){let i=localStorage.getItem("token");if(i){let n=a.clone({headers:a.headers.set("Authorization",`Bearer ${i}`)});return t.handle(n)}return t.handle(a)}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac})}}return o})();var t2=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=f({type:o,bootstrap:[K1]})}static{this.\u0275inj=m({providers:[{provide:Y,useClass:_},{provide:E,useClass:V},{provide:C,useClass:s2,multi:!0},{provide:C,useClass:a2,multi:!0},l1,L],imports:[Q,t1.forRoot(),o2,J]})}}return o})();H();i1.production&&void 0;X().bootstrapModule(t2).catch(o=>console.log(o));
