import{a as N}from"./chunk-4PHGPBSW.js";import"./chunk-UFD7UJFV.js";import{a as D}from"./chunk-4ZIMZLXT.js";import"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$a as x,A as d,Ab as j,Db as f,F as n,G as i,H as l,J as m,M as r,Na as u,Oa as C,Pa as b,Qa as v,Ra as S,Sa as _,Ta as k,Wa as w,X as p,ab as g,cb as y,db as M,ea as I,eb as E,fb as P,gb as O,ub as T,vb as F,z as s,zb as R}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as h}from"./chunk-2R6CW7ES.js";var A=(()=>{class a{constructor(t,e,o,c){this.fcmService=t,this.authService=e,this.alertController=o,this.loadingController=c,this.userId=null}ngOnInit(){let t=localStorage.getItem("token");if(t)try{let e=this.parseJwt(t);e&&e.sub&&(this.userId=e.sub)}catch(e){console.error("Error parsing JWT token:",e)}}parseJwt(t){try{let o=t.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),c=decodeURIComponent(atob(o).split("").map(function(B){return"%"+("00"+B.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(c)}catch(e){return console.error("Error parsing JWT token:",e),null}}refreshFCMToken(){return h(this,null,function*(){let t=yield this.loadingController.create({message:"Refreshing notification settings...",spinner:"circles"});yield t.present();try{let e=yield this.fcmService.refreshFCMToken(this.userId||void 0);yield t.dismiss(),e?yield(yield this.alertController.create({header:"Success",message:"Notification settings refreshed successfully. You should now be able to receive notifications.",buttons:["OK"]})).present():yield(yield this.alertController.create({header:"Error",message:"Failed to refresh notification settings. Please try again later.",buttons:["OK"]})).present()}catch{yield t.dismiss(),yield(yield this.alertController.create({header:"Error",message:"An error occurred while refreshing notification settings. Please try again later.",buttons:["OK"]})).present()}})}static{this.\u0275fac=function(e){return new(e||a)(s(D),s(N),s(R),s(j))}}static{this.\u0275cmp=d({type:a,selectors:[["app-fcm-refresh"]],decls:14,vars:0,consts:[["expand","block",3,"click"],["name","refresh-outline","slot","start"]],template:function(e,o){e&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),r(3,"Notification Settings"),i(),n(4,"ion-card-subtitle"),r(5,"Not receiving notifications?"),i()(),n(6,"ion-card-content")(7,"p"),r(8,"If you're not receiving notifications, you can try refreshing your notification settings."),i(),n(9,"p"),r(10,"This will generate a new notification token and register it with our servers."),i(),n(11,"ion-button",0),m("click",function(){return o.refreshFCMToken()}),l(12,"ion-icon",1),r(13," Refresh Notification Settings "),i()()())},dependencies:[f,u,b,v,S,_,k,g,p],styles:["ion-card[_ngcontent-%COMP%]{margin:16px;border-radius:12px;box-shadow:0 4px 12px #0000001a}ion-card-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600}ion-card-subtitle[_ngcontent-%COMP%]{font-size:.9rem;color:var(--ion-color-medium)}ion-card-content[_ngcontent-%COMP%]{padding:16px}p[_ngcontent-%COMP%]{margin-bottom:12px;font-size:.9rem;line-height:1.4;color:var(--ion-color-dark)}ion-button[_ngcontent-%COMP%]{margin-top:16px}"]})}}return a})();var X=(()=>{class a{constructor(t){this.router=t}ngOnInit(){}goBack(){this.router.navigate(["/tabs/home"])}logout(){localStorage.removeItem("token"),this.router.navigate(["/login"])}static{this.\u0275fac=function(e){return new(e||a)(s(I))}}static{this.\u0275cmp=d({type:a,selectors:[["app-settings"]],decls:22,vars:0,consts:[["slot","start"],[3,"click"],["slot","icon-only","name","arrow-back"],["button","",3,"click"],["name","log-out-outline","slot","start","color","danger"]],template:function(e,o){e&1&&(n(0,"ion-header")(1,"ion-toolbar")(2,"ion-buttons",0)(3,"ion-button",1),m("click",function(){return o.goBack()}),l(4,"ion-icon",2),i()(),n(5,"ion-title"),r(6,"Settings"),i()()(),n(7,"ion-content")(8,"ion-list")(9,"ion-item-group")(10,"ion-item-divider")(11,"ion-label"),r(12,"Notifications"),i()(),l(13,"app-fcm-refresh"),i(),n(14,"ion-item-group")(15,"ion-item-divider")(16,"ion-label"),r(17,"Account"),i()(),n(18,"ion-item",3),m("click",function(){return o.logout()}),l(19,"ion-icon",4),n(20,"ion-label"),r(21,"Logout"),i()()()()())},dependencies:[f,u,C,w,x,g,y,M,E,P,O,T,F,p,A],styles:["ion-item-divider[_ngcontent-%COMP%]{--background: var(--ion-color-light);--color: var(--ion-color-medium);font-size:.9rem;font-weight:600;letter-spacing:.5px;text-transform:uppercase;padding-top:16px}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}"]})}}return a})();export{X as SettingsPage};
